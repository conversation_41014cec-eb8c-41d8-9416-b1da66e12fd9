{% extends 'base.html' %}
{% load tz %}
{% load custom_tags %}
{% load i18n %}
{% load hosts %}
{% load humanize %}
{% get_current_language as LANGUAGE_CODE %}


{% block content %}

{% include "data/utility/table-css.html" %}

{% include 'data/static/tootip-search-wrapper.html' %}
<div class="d-flex d-flex align-items-center" style="background-color: #FAFAFA !important;">
    {% include 'data/common/module-header-tabs.html' %}

    <div class="w-50 d-flex justify-content-end tw-mr-5" id="view-container">
        <div class="{% include "data/utility/table-button.html" %}">
            {% if permission|check_permission:'edit' %}
            <button id='view-sync-contact-action-drawer' type="button" class="max-md:tw-hidden w-125px align-items-center d-flex svg-icon-gray-600 btn py-1 rounded-1 tw-bg-gray-200 hover:tw-bg-gray-300 justify-content-center px-1 create-view-settings-button"
                hx-get="{% url 'new_customerlinkapp_drawer' %}" 
                hx-trigger="click"
                onclick="fillActionContactIds(this),check_permission_action(event, 'edit')"
                hx-target="#manage-view-settings-drawer"
                hx-indicator=".loading-drawer-spinner,.view-form"
                >
                <span class="svg-icon svg-icon-4">
                    <svg width="14" height="16" viewBox="0 0 14 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M8.49968 1.33301H4.66264C4.54298 1.33301 4.48316 1.33301 4.43034 1.35123C4.38363 1.36734 4.34109 1.39363 4.30579 1.4282C4.26587 1.4673 4.23912 1.52081 4.18561 1.62783L1.38561 7.22783C1.25782 7.4834 1.19393 7.61119 1.20927 7.71506C1.22268 7.80576 1.27285 7.88694 1.34798 7.93949C1.43403 7.99967 1.5769 7.99967 1.86264 7.99967H5.99968L3.99968 14.6663L12.1284 6.23655C12.4027 5.95214 12.5398 5.80994 12.5478 5.68826C12.5548 5.58265 12.5112 5.48 12.4303 5.4117C12.3371 5.33301 12.1396 5.33301 11.7445 5.33301H6.99968L8.49968 1.33301Z" stroke="#3B3B3F" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                    </svg>
                </span>
                <span class="">
                    {% if LANGUAGE_CODE == 'ja'%}
                    アクション
                    {% else %}
                    Action
                    {% endif %}
                </span>
            </button>
            {% endif %}
        </div>

        <script>
            {% if open_drawer == 'action_drawer_history' %}
                $(document).ready(function() {
                    setTimeout(function() {
                        document.getElementById('view-sync-contact-action-drawer').click()
                        setTimeout(function() {
                            document.getElementById('history-tab').click()
                        }, 1000)
                    }, 1000)
                })
            {% endif %}
            function fillActionContactIds(elm) {
                // Call your JavaScript function to generate the account IDs dynamically
                var checkboxes = document.querySelectorAll('input[type="checkbox"]');
                var checkedIds = [];
                checkboxes.forEach(function(checkbox) {
                    if (checkbox.checked) {
                        checkedIds.push(checkbox.value);
                    }
                });
                var objIds = checkedIds;
                objIds = objIds.filter(id => id !== 'on');
                // Now set the hx-vals attribute with the updated account IDs
                elm.setAttribute('hx-vals', 'js:{"drawer_type":"contact-action", "section": "action_history", "page": "{{page}}","action_tab":"action", "contact_ids":getSelectedContacts()}');
            }
        </script>

        <div class="{% include "data/utility/header-action-button.html" %}">
            <button class="btn tw-font-[500] tw-rounded-l-lg tw-rounded-r-none tw-border-0 tw-bg-gray-200 hover:tw-bg-gray-300 max-md:tw-hidden w-125px align-items-center d-flex svg-icon-gray-600 py-1 justify-content-center px-1 create-view-settings-button content_review_button" type="button"
                hx-get="{% url 'load_drawer_sync_contacts' %}"
                hx-include="[name='checkbox']"
                hx-vals='{"drawer_type":"shopturbo-view-sync-contacts","page":"contacts","view_id":"{{view_filter.view.id}}","download_view": true,"import_export_type":"export"}'
                hx-target="#manage-view-settings-drawer"
                hx-trigger="click"
                hx-swap="innerHTML"
                >
                <span class="svg-icon svg-icon-4">
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-upload" viewBox="0 0 16 16">
                        <path d="M.5 9.9a.5.5 0 0 1 .5.5v2.5a1 1 0 0 0 1 1h12a1 1 0 0 0 1-1v-2.5a.5.5 0 0 1 1 0v2.5a2 2 0 0 1-2 2H2a2 2 0 0 1-2-2v-2.5a.5.5 0 0 1 .5-.5"/>
                        <path d="M7.646 1.146a.5.5 0 0 1 .708 0l3 3a.5.5 0 0 1-.708.708L8.5 2.707V11.5a.5.5 0 0 1-1 0V2.707L5.354 4.854a.5.5 0 1 1-.708-.708z"/>
                    </svg>
                </span>
                <span class="">
                    {% if LANGUAGE_CODE == 'ja'%}
                    エクスポート
                    {% else %}
                    Export
                    {% endif %}
                </span>
            </button>
            {% if permission|check_permission:'edit' %}
            <button id='view-sync-items' type="button" class="{% include "data/utility/import-button.html" %} content_review_button"
                hx-get="{% url 'load_drawer_sync_contacts' %}"
                hx-vals='{"drawer_type":"shopturbo-view-sync-contacts","view_id":"{{view_filter.view.id}}","page": "contacts","import_export_type":"import"}'
                hx-include="[name='checkbox']"
                hx-target="#manage-view-settings-drawer"
                hx-trigger="click"
                >
                <span class="svg-icon svg-icon-4">
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-download" viewBox="0 0 16 16">
                        <path d="M.5 9.9a.5.5 0 0 1 .5.5v2.5a1 1 0 0 0 1 1h12a1 1 0 0 0 1-1v-2.5a.5.5 0 0 1 1 0v2.5a2 2 0 0 1-2 2H2a2 2 0 0 1-2-2v-2.5a.5.5 0 0 1 .5-.5"/>
                        <path d="M7.646 11.854a.5.5 0 0 0 .708 0l3-3a.5.5 0 0 0-.708-.708L8.5 10.293V1.5a.5.5 0 0 0-1 0v8.793L5.354 8.146a.5.5 0 1 0-.708.708z"/>
                    </svg>
                </span>
                <span class="">
                    {% if LANGUAGE_CODE == 'ja'%}
                    インポート
                    {% else %}
                    Import
                    {% endif %}
                </span>
            </button>
            {% endif %}
        </div>

        {% if permission|check_permission:'edit' %}
            {% if not property_sets %}
                <button class="tw-w-[100px] align-items-center d-flex btn btn-primary btn-md py-1 rounded-1 customer-create-wizard view_form_trigger" type="button"
                    hx-get="{% url 'new_customerlinkapp_drawer' %}" 
                    hx-vals='{"drawer_type":"contacts","view_id": "{{view_filter.view.id}}","set_id":"{{set_id}}" }'
                    hx-target="#customer-drawer"
                    style="height: 32px;"
                    >
                <span class="svg-icon svg-icon-4">
                    <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M8.00065 3.33301V12.6663M3.33398 7.99967H12.6673" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                    </svg>
                </span>

                    <span class="fs-7 ps-1 fw-bolder w-85px">
                        {% if LANGUAGE_CODE == 'ja'%}
                        新規
                        {% else %}
                        New 
                        {% endif %}
                    </span>
                </button>
            {% else %}
                <div class="btn-group tw-h-[32px]">
                    <button class="tw-w-[100px] align-items-center d-flex btn btn-primary btn-md customer-create-wizard view_form_trigger py-1" type="button"
                        hx-get="{% url 'new_customerlinkapp_drawer' %}" 
                        hx-vals = '{"drawer_type":"contacts", "view_id": "{{view_filter.view.id}}","set_id":"{{set_id}}" }'
                        hx-target="#customer-drawer"
                        style="border-radius: 0.475rem 0 0 0.475rem;"
                        >
                        <span class="svg-icon svg-icon-4">
                            <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M8.00065 3.33301V12.6663M3.33398 7.99967H12.6673" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                            </svg>
                        </span>

                        <span class="fs-7 ps-1 fw-bolder w-85px">
                            {% if LANGUAGE_CODE == 'ja'%}
                            新規
                            {% else %}
                            New
                            {% endif %}
                        </span>
                    </button>
                    <button type="button"
                        class="tw-w-[30px] align-items-center d-flex btn btn-primary btn-md dropdown-toggle py-1 tw-pl-2 dropdown-toggle-split"
                        data-bs-toggle="dropdown"
                        aria-expanded="false"
                        style="border-radius: 0 0.475rem 0.475rem 0;border-left: 1px solid #dee2e6;"
                    >
                        <span class="svg-icon svg-icon-4">
                            <svg width=24 height=24 xmlns="http://www.w3.org/2000/svg" viewBox="0 0 320 512" fill="currentColor">
                                <path d="M137.4 374.6c12.5 12.5 32.8 12.5 45.3 0l128-128c9.2-9.2 11.9-22.9 6.9-34.9s-16.6-19.8-29.6-19.8L32 192c-12.9 0-24.6 7.8-29.6 19.8s-2.2 25.7 6.9 34.9l128 128z"/>
                            </svg>
                        </span>
                    </button>
                    <ul class="dropdown-menu tw-max-w-[180px] tw-max-h-[300px] tw-overflow-y-scroll">
                        {% for set in property_sets %}
                        <li>
                            <button class="dropdown-item tw-text-ellipsis tw-overflow-hidden customer-create-wizard" type="button"
                                hx-get="{% url 'new_customerlinkapp_drawer' %}"
                                hx-vals = '{"drawer_type":"contacts", "view_id": "{{view_id}}", "set_id": "{{set.id}}"}'
                                hx-target="#customer-drawer"
                                style="border-radius: 0.475rem 0 0 0.475rem;"
                                >
                                {% if set.name %}
                                    {{ set.name}}
                                {% else %}
                                    {% if LANGUAGE_CODE == 'ja' %}デフォルト{% else %}Default{% endif %}
                                {% endif %}
                            </button>
                        </li>
                        {% endfor %}
                    </ul>
                </div>
            {% endif %}
        {% endif %}
    </div>
</div>

{% include "data/common/advance_search/advance-search-style.html" %}

{% if permission == 'hide' %}
    {% include 'data/static/no-access-page.html' %}
{% else %}

{% if permission|check_permission:'edit' %}
    <div id='modal-load'
        hx-vals='{"object_type": "{{object_type}}","module": "{{menu_key}}","view_id": "{{view_id}}"}'
        hx-get="{% url 'get_bulk_update_properties' %}" 
        hx-trigger='load'
        hx-target="this">
    </div>
{% endif %}

{% if permission|check_permission:'edit' %}
    <div id='modal-load'
        hx-vals='{"object_type": "{{object_type}}","module": "{{menu_key}}","view_id": "{{view_id}}"}'
        hx-get="{% url 'get_bulk_update_properties' %}" 
        hx-trigger='load'
        hx-target="this">
    </div>
{% endif %}

{% comment %} Table Start {% endcomment %}
<div class="w-100 px-0 sanka-table">
    {% include 'data/common/permission-action-warning-message.html' %}
    {% comment %} Views Part {% endcomment %}
    <div class="{% include "data/utility/tab-pane.html" %}" role="tabpanel" style="z-index:4 !important;">
        <div class="{% include "data/utility/table-nav.html" %}">
            <div class="{% include "data/utility/table-content.html" %}" id="view-container">
                {% comment %} Desktop {% endcomment %}
                <div class="max-md:tw-hidden w-75 d-flex align-items-center">
                    <div class="nav-item fs-6 text-gray-900 d-flex align-items-center justify-content-center">
                        <button type="button" class="align-items-center justify-content-center d-flex btn text-gray-600 bg-transparent text-hover-primary tw-px-[5px] create-view-settings-button mb-2 tw-w-[30px]"
                            style="height: 26px;"
                            hx-vals='{"drawer_type":"contacts-view-settings","page": "contacts"}'
                            hx-get="{% url 'new_customerlinkapp_drawer' %}"
                            hx-target="#manage-view-settings-drawer"
                            hx-trigger="click"
                            hx-swap="innerHTML">
                            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="currentColor" class="bi bi-plus" viewBox="0 0 16 16">
                                <path d="M8 4a.5.5 0 0 1 .5.5v3h3a.5.5 0 0 1 0 1h-3v3a.5.5 0 0 1-1 0v-3h-3a.5.5 0 0 1 0-1h3v-3A.5.5 0 0 1 8 4z"/>
                            </svg>
                        </button>
                    </div>

                    {% include 'data/projects/partial-dropdown-view-menu.html' %}

                    <div class="{% include "data/utility/view-menu-nav-item.html" %}">
                        <a class="{% include "data/utility/view-menu-default.html" %}"
                            type="button"
                            href = "{% host_url 'load_object_page' menu_key object_type|object_type_slug:request host 'app' %}">
                            <span class="svg-icon svg-icon-muted svg-icon-2"><svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M22 12C22 17.5 17.5 22 12 22C6.5 22 2 17.5 2 12C2 6.5 6.5 2 12 2C17.5 2 22 6.5 22 12ZM12 6C8.7 6 6 8.7 6 12C6 15.3 8.7 18 12 18C15.3 18 18 15.3 18 12C18 8.7 15.3 6 12 6Z" fill="currentColor"/>
                                </svg>
                            </span>
                        </a>
                        {% if not view_filter.view.title %}
                        <div class="w-20px nav-item justify-content-center d-flex fs-6 text-gray-900">
                            <button type="button" class="{% include "data/utility/view-plus-link.html" %} update-view-settings-button"
                                hx-vals='{"module": "{{menu_key}}", "drawer_type":"contacts-view-settings","page":"contacts","view_id":"{{view_filter.view.id}}"}'
                                hx-get="{% url 'new_customerlinkapp_drawer' %}"
                                hx-target="#manage-update-view-settings-drawer"
                                hx-trigger="click"
                                hx-swap="innerHTML">
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-three-dots-vertical" viewBox="0 0 16 16">
                                    <path d="M9.5 13a1.5 1.5 0 1 1-3 0 1.5 1.5 0 0 1 3 0zm0-5a1.5 1.5 0 1 1-3 0 1.5 1.5 0 0 1 3 0zm0-5a1.5 1.5 0 1 1-3 0 1.5 1.5 0 0 1 3 0z"/>
                                </svg>
                            </button>
                        </div>
                        {% endif %}
                    </div>

                    {% comment %} Put Items here {% endcomment %}
                    {% include 'data/projects/partial-view-menu.html' %}

                </div>

                <div class="tw-hidden max-md:tw-flex tw-w-1/2">
                    <div class="d-flex align-items-center">
                        <!-- Example split danger button -->
                        <div class="btn-group mb-2">
                            <button type="button" class="tw-max-w-[90px] align-items-center d-flex btn bg-white border btn-md tw-px-[10px] customer-create-wizard"
                                style="height: 26px; border-radius: 0.475rem 0 0 0.475rem;"
                                hx-vals='{"drawer_type":"contacts-view-settings","page": "contacts", "view_id":"{{view_filter.view.id}}"}'
                                hx-get="{% url 'new_customerlinkapp_drawer' %}"
                                hx-target="#customer-drawer"
                                hx-trigger="click"
                                hx-swap="innerHTML"
                            >
                                <span class="fs-7 ps-1 fw-bolder tw-text-ellipsis tw-overflow-hidden tw-text-nowrap">
                                    {% if view_filter.view.title %}
                                        {{ view_filter.view.title }}
                                    {% else %}
                                        {% if LANGUAGE_CODE == 'ja'%}ビュー{% else %}View{% endif %}
                                    {% endif %}
                                </span>
                            </button>
                            <button type="button"
                                class="tw-w-[30px] align-items-center d-flex btn btn-white border btn-md  dropdown-toggle py-1 tw-pl-2 dropdown-toggle-split"
                                data-bs-toggle="dropdown"
                                aria-expanded="false"
                                style="height: 26px; border-radius: 0 0.475rem 0.475rem 0;"
                            >
                                <span class="svg-icon svg-icon-4">
                                    <svg width=24 height=24 xmlns="http://www.w3.org/2000/svg" viewBox="0 0 320 512" fill="currentColor">
                                        <path d="M137.4 374.6c12.5 12.5 32.8 12.5 45.3 0l128-128c9.2-9.2 11.9-22.9 6.9-34.9s-16.6-19.8-29.6-19.8L32 192c-12.9 0-24.6 7.8-29.6 19.8s-2.2 25.7 6.9 34.9l128 128z"/>
                                    </svg>
                                </span>
                            </button>
                            <ul class="dropdown-menu tw-max-w-[180px] tw-max-h-[300px] tw-overflow-y-scroll">
                                <li><a class="dropdown-item tw-text-ellipsis tw-overflow-hidden" href="{% host_url 'load_object_page' menu_key object_type|object_type_slug:request host 'app' %}">{% if LANGUAGE_CODE == 'ja' %}デフォルト{% else %}Default View{% endif %}</a></li>
                                <li><a class="dropdown-item tw-text-ellipsis tw-overflow-hidden {% if request.GET.status == 'archived' %}active fw-bolder{% endif %}" href="{% host_url 'load_object_page' menu_key object_type|object_type_slug:request host 'app' %}?status=archived">{% if LANGUAGE_CODE == 'ja' %}アーカイブ{% else %}Archived{% endif %}</a></li>

                                {% for view in views %}
                                    {% if view.title %}
                                        <li><a class="dropdown-item tw-text-ellipsis tw-overflow-hidden" href="{% host_url 'load_object_page' menu_key object_type|object_type_slug:request host 'app' %}?view_id={{view.id}}">{{view.title}}</a></li>
                                    {% endif %}
                                {% endfor %}
                            </ul>
                        </div>
                        <div class="fs-6 text-gray-900 mb-2">
                            <button type="button" class="tw-border-0 tw-bg-transparent tw-text-gray-900 customer-create-wizard"
                                hx-vals='{"drawer_type":"contacts-view-settings","page": "contacts"}'
                                hx-get="{% url 'new_customerlinkapp_drawer' %}"
                                hx-target="#customer-drawer"
                                hx-trigger="click"
                                hx-swap="innerHTML">

                                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="currentColor" class="bi bi-plus" viewBox="0 0 16 16">
                                    <path d="M8 4a.5.5 0 0 1 .5.5v3h3a.5.5 0 0 1 0 1h-3v3a.5.5 0 0 1-1 0v-3h-3a.5.5 0 0 1 0-1h3v-3A.5.5 0 0 1 8 4z"/>
                                </svg>
                            </button>
                        </div>
                    </div>
                </div>

                <div class="d-flex w-50 justify-content-end" id="view-container-1">
                    <div class="max-md:tw-hidden tw-flex">
                        <div class="mb-2 search-wrapper expanded">
                            <div class="d-flex align-items-center">
                                <form id="filter-form-search" method="get" class="w-100">
                                    <div class="d-flex mb-0 position-relative align-items-center" style="height: 26px;">
                                        <span class="svg-icon svg-icon-3 search-icon-view">
                                            <svg width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <path d="M13 13L10.1 10.1M11.6667 6.33333C11.6667 9.27885 9.27885 11.6667 6.33333 11.6667C3.38781 11.6667 1 9.27885 1 6.33333C1 3.38781 3.38781 1 6.33333 1C9.27885 1 11.6667 3.38781 11.6667 6.33333Z" stroke="#8E8C95" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                                            </svg>
                                        </span>
                                        {% if view_id %}
                                        <input type="hidden" name="view_id" value="{{view_id}}">
                                        {% endif %}
                                        <input
                                        id="base-search-input" type="text" name="q" class="form-control bg-white ps-12 pe-16 h-25px tw-rounded-lg"
                                        value="{% if advance_search_filter %}{% for k, v in advance_search_filter.items %}{% if k|search_custom_field_object_contacts:request %}{% with channel_column=k|search_custom_field_object_contacts:request %}{{channel_column.name}}{% endwith %}{% else %}{{ k|display_column_contacts:request }}{% endif %} {% translate_lang v.key|display_filter_rule:LANGUAGE_CODE LANGUAGE_CODE %} {{ v.value }}{% if not forloop.last %}, {% endif %}{% endfor %}{% elif search_q %}{{ search_q }}{% else %}{% endif %}"
                                        {% if page_type != 'companies' %}
                                        placeholder={% if LANGUAGE_CODE == 'ja' %} "検索" {% else %} "Search" {% endif %}
                                        {% else %}
                                        placeholder={% if LANGUAGE_CODE == 'ja' %} "検索" {% else %} "Search" {% endif %}
                                        {% endif %}
                                        onkeypress="if(event.keyCode == 13)document.forms['filter-form-search'].submit();"
                                        >
                                        {% if request.GET.status == 'archived' %}
                                        <input type="hidden" value="archived" name="status">
                                        {% endif %}
                                        <span class="svg-icon svg-icon-muted svg-icon-3 filter-icon filter-drawer-btn"
                                            hx-get="{% url 'advance_search_drawer' %}"
                                            hx-vals='{"object_type": "{{object_type}}", "module": "{{menu_key}}", "search_scope":"True", "view_id": "{{view_id}}"}'
                                            hx-indicator=".loading-drawer-spinner"
                                            hx-target="#filter-drawer-content"
                                            hx-trigger="click"
                                            hx-swap="innerHTML"
                                        >
                                            <svg width="14" height="12" viewBox="0 0 14 12" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <path d="M1 3.33301L9 3.33301M9 3.33301C9 4.43758 9.89543 5.33301 11 5.33301C12.1046 5.33301 13 4.43758 13 3.33301C13 2.22844 12.1046 1.33301 11 1.33301C9.89543 1.33301 9 2.22844 9 3.33301ZM5 8.66634L13 8.66634M5 8.66634C5 9.77091 4.10457 10.6663 3 10.6663C1.89543 10.6663 1 9.77091 1 8.66634C1 7.56177 1.89543 6.66634 3 6.66634C4.10457 6.66634 5 7.56177 5 8.66634Z" stroke="#8E8C95" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                                            </svg>
                                        </span>
                                        <span class="svg-icon {% if advance_search.is_active %}svg-icon-primary{%else%}svg-icon-muted{% endif %} svg-icon-3 filter-icon filter-drawer-btn me-7"
                                            hx-get="{% url 'advance_search_drawer' %}"
                                            hx-vals='{"object_type": "contacts", "module": "{{menu_key}}", "view_id": "{{view_id}}"}'
                                            hx-indicator=".loading-drawer-spinner"
                                            hx-target="#filter-drawer-content"
                                            hx-trigger="click"
                                            hx-swap="innerHTML"
                                        >
                                            <svg width="14" height="10" viewBox="0 0 14 10" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <path d="M3 5H11M1 1H13M5 9H9" stroke="#8E8C95" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                                            </svg>   
                                        </span>
                                        {% if view_id %}<input type="hidden" value="{{view_id}}" name="view_id">{% endif %}
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>

                    <div class="{% include "data/utility/view-menu-search.html" %}">
                        <button
                            onclick="openSearch()"
                            class="tw-w-[28px] tw-h-[26px] tw-justify-center tw-items-center tw-border-0 tw-bg-transparent tw-mb-2 tw-cursor-pointer hover-tooltip" type="button"
                            >
                            <span class="search-wrapper-tooltip hover-tooltip-text">
                                {% if LANGUAGE_CODE == 'ja' %}検索{% else %}Search{% endif %}
                            </span>
                            <span class="tw-flex svg-icon svg-icon-3">
                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                                    <path d="M21.7 18.9L18.6 15.8C17.9 16.9 16.9 17.9 15.8 18.6L18.9 21.7C19.3 22.1 19.9 22.1 20.3 21.7L21.7 20.3C22.1 19.9 22.1 19.3 21.7 18.9Z" fill="black" />
                                    <path opacity="0.3" d="M11 20C6 20 2 16 2 11C2 6 6 2 11 2C16 2 20 6 20 11C20 16 16 20 11 20ZM11 4C7.1 4 4 7.1 4 11C4 14.9 7.1 18 11 18C14.9 18 18 14.9 18 11C18 7.1 14.9 4 11 4ZM8 11C8 9.3 9.3 8 11 8C11.6 8 12 7.6 12 7C12 6.4 11.6 6 11 6C8.2 6 6 8.2 6 11C6 11.6 6.4 12 7 12C7.6 12 8 11.6 8 11Z" fill="black" />
                                </svg>
                            </span>
                        </button>
                    </div>

                    <div class="{% include "data/utility/table-button.html" %}">
                        {% if permission|check_permission:'edit' %}
                        <button id='view-sync-items' type="button" class="max-md:tw-block tw-hidden tw-w-[28px] tw-h-[26px] tw-justify-center tw-items-center tw-border-0 tw-bg-transparent tw-mb-2 tw-cursor-pointer customer-manage-wizard content_review_button hover-tooltip"
                            hx-get="{% url 'new_customerlinkapp_drawer' %}"
                            hx-vals='{"drawer_type":"sync_contacts","page": "contacts"}'
                            hx-include="[name='checkbox']"
                            hx-target="#customer-manage-drawer"
                            hx-trigger="click"
                            >
                            <span class="search-wrapper-tooltip hover-tooltip-text">
                                {% if LANGUAGE_CODE == 'ja' %}同期{% else %}Sync{% endif %}
                            </span>
                            <span class="tw-flex svg-icon svg-icon-3">
                                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M14.5 20.7259C14.6 21.2259 14.2 21.826 13.7 21.926C13.2 22.026 12.6 22.0259 12.1 22.0259C9.5 22.0259 6.9 21.0259 5 19.1259C1.4 15.5259 1.09998 9.72592 4.29998 5.82592L5.70001 7.22595C3.30001 10.3259 3.59999 14.8259 6.39999 17.7259C8.19999 19.5259 10.8 20.426 13.4 19.926C13.9 19.826 14.4 20.2259 14.5 20.7259ZM18.4 16.8259L19.8 18.2259C22.9 14.3259 22.7 8.52593 19 4.92593C16.7 2.62593 13.5 1.62594 10.3 2.12594C9.79998 2.22594 9.4 2.72595 9.5 3.22595C9.6 3.72595 10.1 4.12594 10.6 4.02594C13.1 3.62594 15.7 4.42595 17.6 6.22595C20.5 9.22595 20.7 13.7259 18.4 16.8259Z" fill="currentColor"/>
                                    <path opacity="0.3" d="M2 3.62592H7C7.6 3.62592 8 4.02592 8 4.62592V9.62589L2 3.62592ZM16 14.4259V19.4259C16 20.0259 16.4 20.4259 17 20.4259H22L16 14.4259Z" fill="currentColor"/>
                                </svg>
                            </span>
                        </button>
                        {% endif %}
                    </div>
                </div>
            </div>
            {% comment %} Desktop {% endcomment %}


            <div id="view-header-container" class="tw-hidden w-100 px-5">
                <div class="justify-content-between align-items-center flex-row d-flex">
                    <div class="w-100">
                        {% include 'data/common/select-all-in-view-button.html' %}
                        
                        {% if permission|check_permission:'edit' %}
                        <button class="py-1 rounded-1 btn btn-sm btn-light-primary fw-bold mt-2 mb-1" onclick="bulk_check_toggle(),check_permission_action(event, 'edit', 'edit_bulk_modal')">
                            <span>
                                {% if LANGUAGE_CODE == 'ja'%}
                                編集
                                {% else %}
                                Edit
                                {% endif %}
                            </span>
                        </button>
                        <button class="py-1 rounded-1 btn btn-sm btn-light-warning fw-bold mt-2 mb-1" onclick="check_permission_action(event, 'edit')" name="bulk_duplicate" type="submit" form="contacts-form">
                            {% if LANGUAGE_CODE == 'ja'%}
                            複製
                            {% else %}
                            Duplicate
                            {% endif %}
                        </button>
                        {% endif %}
                        {% if permission|check_permission:'archive' %}
                            <button class="py-1 rounded-1 btn btn-sm btn-light-success me-1 fw-bold mt-2 mb-1" onclick="check_permission_action(event, 'archive', 'manage_restore_bulk')">
                                {% if LANGUAGE_CODE == 'ja'%}
                                有効化
                                {% else %}
                                Activate
                                {% endif %}
                            </button>
                            <button class="btn btn-sm btn-light-danger py-1 rounded-1 me-1 fw-bold mt-2 mb-1"  onclick="check_permission_action(event, 'archive', 'manage_delete_bulk')">
                                {% if LANGUAGE_CODE == 'ja'%}
                                アーカイブ
                                {% else %}
                                Archive
                                {% endif %}
                            </button>
                        {% endif %}
                        <script>
                            document.body.addEventListener("hideTaskBulkActionBtn", function(evt){
                                document.getElementById('task-bulk-action-container').classList.add('d-none')
                                document.getElementById('task-view-contianer').classList.remove('d-none')
                            })
                        </script>
                    </div>

                    <div class="d-flex">
                        {% if permission|check_permission:'edit' %}

                        <button id='view-sync-contact-action-drawer' type="button" class="ml-auto ms-auto {% include "data/utility/gray-header-button.html" %} create-view-settings-button me-2"
                            hx-get="{% url 'new_customerlinkapp_drawer' %}" 
                            hx-trigger="click"
                            onclick="fillActionContactIds(this),check_permission_action(event, 'edit')"
                            hx-target="#manage-view-settings-drawer"
                            hx-indicator=".loading-drawer-spinner,.view-form"
                            >
                            <span class="svg-icon svg-icon-4">
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-magic" viewBox="0 0 16 16">
                                    <path d="M9.5 2.672a.5.5 0 1 0 1 0V.843a.5.5 0 0 0-1 0zm4.5.035A.5.5 0 0 0 13.293 2L12 3.293a.5.5 0 1 0 .707.707zM7.293 4A.5.5 0 1 0 8 3.293L6.707 2A.5.5 0 0 0 6 2.707zm-.621 2.5a.5.5 0 1 0 0-1H4.843a.5.5 0 1 0 0 1zm8.485 0a.5.5 0 1 0 0-1h-1.829a.5.5 0 0 0 0 1zM13.293 10A.5.5 0 1 0 14 9.293L12.707 8a.5.5 0 1 0-.707.707zM9.5 11.157a.5.5 0 0 0 1 0V9.328a.5.5 0 0 0-1 0zm1.854-5.097a.5.5 0 0 0 0-.706l-.708-.708a.5.5 0 0 0-.707 0L8.646 5.94a.5.5 0 0 0 0 .707l.708.708a.5.5 0 0 0 .707 0l1.293-1.293Zm-3 3a.5.5 0 0 0 0-.706l-.708-.708a.5.5 0 0 0-.707 0L.646 13.94a.5.5 0 0 0 0 .707l.708.708a.5.5 0 0 0 .707 0z"/>
                                </svg>
                            </span>
                            <span class="">
                                {% if LANGUAGE_CODE == 'ja'%}
                                アクション
                                {% else %}
                                Action
                                {% endif %}
                            </span>
                        </button>

                        <div class="{% include "data/utility/table-button.html" %}">
                            <button id='view-sync-items' type="button" class="{% include "data/utility/gray-header-button.html" %} create-view-settings-button content_review_button"
                                hx-get="{% url 'load_drawer_sync_contacts' %}"
                                hx-include="[name='checkbox']"
                                hx-vals='{"drawer_type":"shopturbo-view-sync-contacts","page":"contacts","view_id":"{{view_filter.view.id}}","download_view": true,"import_export_type":"export"}'
                                hx-target="#manage-view-settings-drawer" 
                                hx-trigger="click"
                                hx-swap="innerHTML"
                                >
                                <span class="svg-icon svg-icon-4">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-upload" viewBox="0 0 16 16">
                                        <path d="M.5 9.9a.5.5 0 0 1 .5.5v2.5a1 1 0 0 0 1 1h12a1 1 0 0 0 1-1v-2.5a.5.5 0 0 1 1 0v2.5a2 2 0 0 1-2 2H2a2 2 0 0 1-2-2v-2.5a.5.5 0 0 1 .5-.5"/>
                                        <path d="M7.646 1.146a.5.5 0 0 1 .708 0l3 3a.5.5 0 0 1-.708.708L8.5 2.707V11.5a.5.5 0 0 1-1 0V2.707L5.354 4.854a.5.5 0 1 1-.708-.708z"/>
                                    </svg>
                                </span>
                                <span class="">
                                    {% if LANGUAGE_CODE == 'ja'%}
                                    エクスポート
                                    {% else %}
                                    Export
                                    {% endif %}
                                </span>
                            </button>
                        </div>
                        {% endif %}
                    </div>
                </div>
                {% include 'data/common/select-all-in-view-record-msg.html' %}
            </div>
        </div>
    </div>
    {% comment %} End of Views {% endcomment %}

    <div class="{% if search_q %}max-md:tw-flex {% else %}max-md:tw-hidden {% endif %}tw-hidden tw-items-center tw-py-2 border-bottom border-bottom-1 tw-transition-all tw-duration-300" id="search-bar">
        <form id="filter-form-search" method="get" class="w-100">
            <div class="d-flex mb-0 position-relative align-items-center" style="height: 26px;">
                <span class="svg-icon svg-icon-3 search-icon-view">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                        <path d="M21.7 18.9L18.6 15.8C17.9 16.9 16.9 17.9 15.8 18.6L18.9 21.7C19.3 22.1 19.9 22.1 20.3 21.7L21.7 20.3C22.1 19.9 22.1 19.3 21.7 18.9Z" fill="black" />
                        <path opacity="0.3" d="M11 20C6 20 2 16 2 11C2 6 6 2 11 2C16 2 20 6 20 11C20 16 16 20 11 20ZM11 4C7.1 4 4 7.1 4 11C4 14.9 7.1 18 11 18C14.9 18 18 14.9 18 11C18 7.1 14.9 4 11 4ZM8 11C8 9.3 9.3 8 11 8C11.6 8 12 7.6 12 7C12 6.4 11.6 6 11 6C8.2 6 6 8.2 6 11C6 11.6 6.4 12 7 12C7.6 12 8 11.6 8 11Z" fill="black" />
                    </svg>
                </span>
                {% if view_id %}
                <input type="hidden" name="view_id" value="{{view_id}}">
                {% endif %}
                <input
                id="base-search-input-mobile" type="text" name="q" class="form-control bg-white ps-12 pe-16"
                value="{% if advance_search_filter %}{% for k, v in advance_search_filter.items %}{% if k|search_custom_field_object_contacts:request %}{% with channel_column=k|search_custom_field_object_contacts:request %}{{channel_column.name}}{% endwith %}{% else %}{{ k|display_column_contacts:request }}{% endif %} {% translate_lang v.key|display_filter_rule:LANGUAGE_CODE LANGUAGE_CODE %} {{ v.value }}{% if not forloop.last %}, {% endif %}{% endfor %}{% elif search_q %}{{ search_q }}{% else %}{% endif %}"
                {% if page_type != 'companies' %}
                placeholder={% if LANGUAGE_CODE == 'ja' %} "検索" {% else %} "Search" {% endif %}
                {% else %}
                placeholder={% if LANGUAGE_CODE == 'ja' %} "検索" {% else %} "Search" {% endif %}
                {% endif %}
                onkeypress="if(event.keyCode == 13)document.forms['filter-form-search'].submit();"
                >
                {% if request.GET.status == 'archived' %}
                <input type="hidden" value="archived" name="status">
                {% endif %}
                <span class="svg-icon svg-icon-muted svg-icon-3 filter-icon filter-drawer-btn me-7"
                    hx-get="{% url 'advance_search_drawer' %}"
                    hx-vals='{"object_type": "{{object_type}}", "module": "{{menu_key}}", "search_scope":"True", "view_id": "{{view_id}}"}'
                    hx-indicator=".loading-drawer-spinner"
                    hx-target="#filter-drawer-content"
                    hx-trigger="click"
                    hx-swap="innerHTML"
                >
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                        <path fill="black" d="M19.43 12.98c.04-.32.07-.65.07-.98s-.03-.66-.07-.98l2.11-1.65a.5.5 0 0 0 .12-.63l-2-3.46a.5.5 0 0 0-.6-.22l-2.49 1a7.03 7.03 0 0 0-1.7-.98l-.38-2.65A.5.5 0 0 0 14 2h-4a.5.5 0 0 0-.5.45l-.38 2.65c-.63.25-1.21.57-1.74.98l-2.49-1a.5.5 0 0 0-.6.22l-2 3.46a.5.5 0 0 0 .12.63L4.57 11c-.04.32-.07.65-.07.98s.03.66.07.98l-2.11 1.65a.5.5 0 0 0-.12.63l2 3.46a.5.5 0 0 0 .6.22l2.49-1c.53.41 1.11.73 1.74.98l.38 2.65c.03.24.24.45.5.45h4c.26 0 .47-.21.5-.45l.38-2.65c.63-.25 1.21-.57 1.74-.98l2.49 1a.5.5 0 0 0 .6-.22l2-3.46a.5.5 0 0 0-.12-.63l-2.11-1.65zM12 15.5A3.5 3.5 0 1 1 12 8a3.5 3.5 0 0 1 0 7.5z"/>
                    </svg>
                </span>
                <span class="svg-icon {% if advance_search.is_active %}svg-icon-primary{%else%}svg-icon-muted{% endif %} svg-icon-3 filter-icon filter-drawer-btn"
                    hx-get="{% url 'advance_search_drawer' %}"
                    hx-vals='{"object_type": "contacts", "module": "{{menu_key}}", "view_id": "{{view_id}}"}'
                    hx-indicator=".loading-drawer-spinner"
                    hx-target="#filter-drawer-content"
                    hx-trigger="click"
                    hx-swap="innerHTML"
                >
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M21 7H3C2.4 7 2 6.6 2 6V4C2 3.4 2.4 3 3 3H21C21.6 3 22 3.4 22 4V6C22 6.6 21.6 7 21 7Z" fill="currentColor"/>
                        <path opacity="0.3" d="M21 14H3C2.4 14 2 13.6 2 13V11C2 10.4 2.4 10 3 10H21C21.6 10 22 10.4 22 11V13C22 13.6 21.6 14 21 14ZM22 20V18C22 17.4 21.6 17 21 17H3C2.4 17 2 17.4 2 18V20C2 20.6 2.4 21 3 21H21C21.6 21 22 20.6 22 20Z" fill="currentColor"/>
                    </svg>
                </span>
                <input type="hidden" value="{{view_id}}" name="view_id">
            </div>
        </form>
    </div>

    <div class="mb-2 d-none" id="d-select-additional-options">
        <div class="status bg-light-dark text-center pt-4 pb-3 px-5 rounded" >

            <span id="contacts-selects-information">
            {% if LANGUAGE_CODE == 'ja'%}
            このページのすべての連絡先 ({{paginator_item_end}}) が選択されています。
            {% else %}
                All contacts ({{paginator_item_end}}) on this page are selected.
            {% endif %}
            </span>
            <a onclick="toggleText()"
                class="btn btn-dark"
                data-bs-toggle="collapse"
                id="select-additional-options-toggle"
                role="button"
                aria-expanded="false"
                aria-controls="collapseExample">
                {% if LANGUAGE_CODE == 'ja'%}
                    すべて ({{paginator.count}}) の連絡先を選択
                {% else %}
                    Select All ({{paginator.count}}) contacts
                {% endif %}
            </a>
        </div>
    </div>


    <form method="POST" action="{% host_url 'load_object_page' menu_key object_type|object_type_slug:request host 'app' %}" id="contacts-form">
        {% csrf_token %}
        <div class="flex-lg-row-fluid">
            <div class="pt-0">
                <div class="w-100 row row-eq-height d-flex flex-wrap g-6 gy-5" id="contact_company_cards">

                {% if config_view == 'list' %}
                <div class="table-responsive px-3" style="max-height: 75vh;">
                    <table id="contacts-table" class="{% include "data/utility/table.html" %}">
                        <thead class="{% include "data/utility/table-header.html" %} position-sticky">
                            <tr>
                                {% for contacts_column in contacts_columns %}
                                    <th {% if contacts_column == 'checkbox' %}
                                            class="{% include 'data/utility/column-checkbox-size.html' %}"
                                        {% elif contacts_column == 'contact_id'  %}
                                            class="{% include 'data/utility/column-id-size.html' %}"
                                        {% else %}
                                            class="text-nowrap"
                                        {% endif %}>
                                        {% if contacts_column != 'checkbox' %}
                                            {% if contacts_column|search_custom_field_object_contacts:request %}
                                                {% with channel_column=contacts_column|search_custom_field_object_contacts:request %}
                                                    {{channel_column.name|display_column_contacts:request}}
                                                {% endwith %}
                                            {% elif contacts_column|search_channel_objects:request %}
                                                {% with channel_column=contacts_column|search_channel_objects:request %}
                                                    {{channel_column}}
                                                {% endwith %}
                                            {% elif " - line user id" in contacts_column|lower %}
                                                {% with channel_column=contacts_column|get_line_name_channel %}
                                                    {{channel_column}}
                                                {% endwith %}
                                            {% else %}
                                                {% with contacts_col_name=contacts_column|display_column_contacts:request %}
                                                    {{contacts_col_name}}
                                                {% endwith %}
                                            {% endif %}
                                        {% endif %}
                                    </th>
                                    {% if contacts_column == 'contact_id'  %}
                                    {% comment %} <th class="" style="width: 20px;">
                                    </th> {% endcomment %}
                                    {% endif %}
                                {% endfor %}
                            </tr>
                        </thead>
                        <tbody class="fs-6">

                            <!-- DEBUG: Total contacts found: {{ contacts|length }} -->
                            <script>
                                console.log('🔍 DEBUG: Template rendering {{ contacts|length }} contacts');
                                console.log('🔍 DEBUG: Search query: "{{ search_q }}"');
                                console.log('🔍 DEBUG: View ID: "{{ view_filter.view.id }}"');
                                console.log('🔍 DEBUG: Target: "{{ target }}"');
                            </script>
                            {% for contact in contacts %}
                                <tr id="row-{{contact.id}}"
                                hx-get="{% host_url 'contact_row_detail' contact.id host 'app' %}"
                                hx-vals='{"view_id": "{{view_filter.view.id}}", "selected_contact_id": "{{selected_contact_id}}", "page": "{{page}}", "target":"{{target}}", "has_checkbox": "{% if 'checkbox' in contacts_columns %}true{% else %}false{% endif %}"}'
                                hx-trigger="load"
                                hx-indicator=".row_load-{{contact.id}}"
                                hx-on::htmx:response-error="console.error('Failed to load contact row for contact {{contact.contact_id}}:', event.detail); this.innerHTML = '<td colspan={{contacts_columns|length}}><div class=text-danger>Error loading contact data for contact {{contact.contact_id}}</div></td>';">
                                    <td class="d-flex justify-content-center w-100">
                                        <style>
                                            /* Styles for the spinner */
                                            .row_load-{{contact.id}} {
                                                display: none; /* Initially hidden */
                                            }
                                            .htmx-request .row_load-{{contact.id}},
                                            .htmx-request.row_load-{{contact.id}} {
                                                display: inline-block; /* Display during htmx request */
                                            }
                                        </style>
                                        <!-- Spinner icon -->
                                        <span class="spinner-border spinner-border-lg text-secondary row_load-{{contact.id}}" role="status">
                                            <span class="visually-hidden">Loading...</span>
                                        </span>
                                    </td>
                                </tr>
                            {% empty %}
                                <script>console.log('🔍 DEBUG: No contacts found in template context');</script>
                                <tr>
                                    <td colspan="{{ contacts_columns|length|default:'100' }}" class="text-center text-muted py-4">
                                        {% if search_q %}
                                            {% if LANGUAGE_CODE == 'ja' %}
                                                検索結果が見つかりません: "{{ search_q }}"
                                            {% else %}
                                                No contacts found for search: "{{ search_q }}"
                                            {% endif %}
                                            <br>
                                            <small class="text-muted">
                                                {% if LANGUAGE_CODE == 'ja' %}
                                                    検索条件を変更してもう一度お試しください
                                                {% else %}
                                                    Try adjusting your search criteria
                                                {% endif %}
                                            </small>
                                        {% else %}
                                            {% if LANGUAGE_CODE == 'ja' %}
                                                連絡先が見つかりません
                                            {% else %}
                                                No contacts found
                                            {% endif %}
                                        {% endif %}
                                    </td>
                                </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>

                {% elif config_view == 'card' %}

                    {% for contact in contacts %}
                    <div class="col-md-6 col-lg-4 pt-5">
                        <div class="card h-100 border">
                            <div class="card-body position-relative">
                                <input
                                    style="left:10px; top:10px"
                                    id="contact-selection-{{contact.id}}"
                                    class="form-check-input position-absolute contact-selection cursor-pointer check_input"
                                    type="checkbox" name="checkbox" value="{{contact.id}}"
                                    onclick="checking_checkbox(this, event)"
                                />
                                <div class="text-center mb-3">
                                    <a class="text-dark cursor-pointer customer-manage-wizard"
                                        hx-get="{% host_url 'load_explore_profile' contact.id host 'app' %}"
                                        hx-target="#customer-manage-drawer"
                                        hx-trigger="click"
                                        >

                                        {% if contact.image_url %}
                                        <div class="symbol symbol-50px">
                                            <img alt="Pic" src="{{ contact.image_url }}" />
                                        </div>
                                        {% elif contact.image_file %}
                                        <div class="symbol symbol-50px">
                                            <img alt="Pic" src="{{ contact.image_file.url }}" />
                                        </div>
                                        {% else %}
                                        <span class="svg-icon svg-icon-muted svg-icon-3hx">
                                            <svg class="h-30px w-30px" width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <path opacity="0.3" d="M16.5 9C16.5 13.125 13.125 16.5 9 16.5C4.875 16.5 1.5 13.125 1.5 9C1.5 4.875 4.875 1.5 9 1.5C13.125 1.5 16.5 4.875 16.5 9Z" fill="currentColor"/>
                                            <path d="M9 16.5C10.95 16.5 12.75 15.75 14.025 14.55C13.425 12.675 11.4 11.25 9 11.25C6.6 11.25 4.57499 12.675 3.97499 14.55C5.24999 15.75 7.05 16.5 9 16.5Z" fill="currentColor"/>
                                            <rect x="7" y="6" width="4" height="4" rx="2" fill="currentColor"/>
                                            </svg>
                                        </span>
                                        {% endif %}
                                    </a>

                                </div>
                                <div class="d-flex flex-column justify-content-center">
                                    <a
                                        hx-get="{% host_url 'load_explore_profile' contact.id host 'app' %}"
                                        hx-target="#customer-manage-drawer"
                                        hx-trigger="click"
                                        class="text-center mb-0 text-dark text-hover-primary fw-bolder cursor-pointer customer-manage-wizard">
                                        {{contact|display_contact_name:LANGUAGE_CODE}}

                                    </a>
                                </div>


                                {% if contact.bio %}
                                    <div class="text-center fw-bold fs-6 text-gray-500 mb-3">
                                        {% if contact.bio|length > 120 %}
                                            {{contact.bio|safe|truncatewords:5|truncatechars:50}}
                                        {% else %}
                                            {{contact.bio|safe|truncatewords:10|truncatechars:50}}
                                        {% endif %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    {% endfor %}


                {% endif %}

            </div>
            <div class="{% include "data/utility/pagination.html" %}">
                {% if LANGUAGE_CODE == 'ja'%}
                {{paginator_item_begin}}–{{paginator_item_end}} の {{paginator.count}} 件
                {% else %}
                Viewing {{paginator_item_begin}}–{{paginator_item_end}} of {{paginator.count}} results
                {% endif %}
                <div class="ms-5">
                    {% if page_content.has_previous %}
                        <a class="btn tw-bg-gray-200 hover:tw-bg-gray-300 w-100px" href="{% host_url 'load_object_page' menu_key object_type|object_type_slug:request host 'app' %}?page=1&{% query_transform %}">&laquo;
                            {% if LANGUAGE_CODE == 'ja'%}
                            最初
                            {% else %}
                            First
                            {% endif %}
                        </a>
                        <a class="btn tw-bg-gray-200 hover:tw-bg-gray-300 w-100px" href="{% host_url 'load_object_page' menu_key object_type|object_type_slug:request host 'app' %}?page={{ page_content.previous_page_number }}&{% query_transform %}">
                            {% if LANGUAGE_CODE == 'ja'%}
                            前
                            {% else %}
                            Previous
                            {% endif %}
                        </a>
                    {% endif %}

                    {% if page_content.has_next %}
                        <a class="btn tw-bg-gray-200 hover:tw-bg-gray-300 w-100px" href="{% host_url 'load_object_page' menu_key object_type|object_type_slug:request host 'app' %}?page={{ page_content.next_page_number }}&{% query_transform %}">
                            {% if LANGUAGE_CODE == 'ja'%}
                            次
                            {% else %}
                            Next
                            {% endif %}
                        </a>
                        <a class="btn tw-bg-gray-200 hover:tw-bg-gray-300 w-100px" href="{% host_url 'load_object_page' menu_key object_type|object_type_slug:request host 'app' %}?page={{ page_content.paginator.num_pages }}&{% query_transform %}">
                            {% if LANGUAGE_CODE == 'ja'%}
                            最後
                            {% else %}
                            Last
                            {% endif %}
                            &raquo;</a>
                    {% endif %}
                </div>
            </div>
        </div>


        <div class="modal fade" tabindex="-1" id="manage_contacts_addtolist">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header pb-0 border-0 justify-content-end">
                        <div class="btn btn-icon btn-sm btn-active-light-primary ms-2" data-bs-dismiss="modal" aria-label="Close">
                        </div>
                    </div>
                    <div class="modal-body pb-0">
                        <div class="mb-13 text-center">
                            <h3 class="modal-title">

                                {% if LANGUAGE_CODE == 'ja'%}
                                リストに連絡先を追加する
                                {% else %}
                                Adding Contacts to List
                                {% endif %}

                            </h3>
                            <p class="text-muted fw-bold fs-5">
                                {% if LANGUAGE_CODE == 'ja'%}
                                選択したリストに追加するアカウントを選択
                                {% else %}
                                Choose Accounts to Add Selected List
                                {% endif %}

                            </p>
                        </div>
                        <div class="border-bottom">
                            <div class="fv-rowd-flex flex-column mb-8">
                                <label class="{% include 'data/utility/form-label.html' %}">
                                    <span class="required">
                                        {% if LANGUAGE_CODE == 'ja'%}
                                        連絡先リスト
                                        {% else %}
                                        Contacts List
                                        {% endif %}
                                    </span>
                                </label>
                                <div class="">
                                    <select class="bg-white form-select form-select-solid border"
                                    data-control="select2"
                                    {% if LANGUAGE_CODE == 'ja'%}
                                    data-placeholder="連絡先リスト"
                                    {% else %}
                                    data-placeholder="Contacs List"
                                    {% endif %}
                                    data-allow-clear="true"
                                    multiple="multiple"
                                    name="account_addtolist"
                                    >
                                        <option></option>
                                        {% for contact_list in contact_lists %}

                                            <option value="{{contact_list.id}}">{{contact_list.name}}</option>

                                        {% endfor %}

                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>

                    <input name='flag_all' id='flag_all' class="flag_all" hidden type="checkbox" ></input>
                    {% if view_id %}
                    <input type="hidden" value="{{view_id}}" name="view_id">
                    {% endif %}

                    <div class="modal-footer border-0">
                        <button name="bulk_update_addtolist" type="submit" class="btn btn-dark">{% if LANGUAGE_CODE == 'ja'%}編集を保存{% else %}Save Updates{% endif %}</button>
                    </div>
                </div>
            </div>
        </div>


        <div class="modal fade" tabindex="-1" id="manage_restore_bulk">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header pb-0 border-0 justify-content-end">
                        <div class="btn btn-icon btn-sm btn-active-light-primary ms-2" data-bs-dismiss="modal" aria-label="Close">
                        </div>
                    </div>
                    <div class="modal-body pb-0">
                        <div class="mb-13 text-center">
                            <h3 class="modal-title">
                                {% if LANGUAGE_CODE == 'ja'%}
                                一括有効化の確認
                                {% else %}
                                Bulk Activate Confirmation
                                {% endif %}
                            </h3>
                        </div>
                        <div class="border-bottom">
                            <div class="fv-rowd-flex flex-column mb-8">
                                <label class="{% include 'data/utility/form-label.html' %}">
                                    <span class="">
                                        {% if LANGUAGE_CODE == 'ja'%}
                                        これらの連絡先を有効化してもよろしいですか?
                                        {% else %}
                                        Are you sure to activate these contacts?
                                        {% endif %}
                                    </span>
                                </label>

                            </div>
                        </div>
                    </div>

                    <input name='flag_all' class="flag_all" hidden></input>

                    <div class="modal-footer border-0">
                        <button name="bulk_restore_contacts" type="submit" class="btn btn-success">
                            {% if LANGUAGE_CODE == 'ja'%}
                            有効化
                            {% else %}
                            Activate
                            {% endif %}
                        </button>
                        <a data-bs-dismiss="modal" class="btn border bg-gray-200">
                            {% if LANGUAGE_CODE == 'ja'%}
                            キャンセル
                            {% else %}
                            Cancel
                            {% endif %}
                        </a>
                    </div>
                </div>
            </div>
        </div>


        <div class="modal fade" tabindex="-1" id="manage_delete_bulk">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header pb-0 border-0 justify-content-end">
                        <div class="btn btn-icon btn-sm btn-active-light-primary ms-2" data-bs-dismiss="modal" aria-label="Close">
                        </div>
                    </div>
                    <div class="modal-body pb-0">
                        <div class="mb-13 text-center">
                            <h3 class="modal-title">

                                {% if LANGUAGE_CODE == 'ja'%}
                                一括アーカイブの確認
                                {% else %}
                                Bulk Archive Confirmation
                                {% endif %}

                            </h3>
                        </div>
                        <div class="border-bottom">
                            <div class="fv-rowd-flex flex-column mb-8">
                                <label class="{% include 'data/utility/form-label.html' %}">
                                    <span class="">
                                        {% if LANGUAGE_CODE == 'ja'%}
                                        選択されたレコードをアーカイブしてもよろしいですか?
                                        {% else %}
                                        Are you sure to archive selected records?
                                        {% endif %}
                                    </span>
                                </label>

                            </div>
                        </div>
                    </div>

                    <input name='flag_all' class="flag_all" hidden></input>

                    <div class="modal-footer border-0">
                        <button name="bulk_delete_contacts" type="submit" class="btn btn-danger">

                            {% if LANGUAGE_CODE == 'ja'%}
                            アーカイブ
                            {% else %}
                            Archive
                            {% endif %}

                        </button>
                        <a data-bs-dismiss="modal" class="btn border bg-gray-200">
                            {% if LANGUAGE_CODE == 'ja'%}
                            キャンセル
                            {% else %}
                            Cancel
                            {% endif %}
                        </a>
                    </div>
                </div>
            </div>
        </div>

    </form>

</div>
{% comment %} =========DRAWER CONTENT===================================================================== {% endcomment %}
<style>
    .daterangepicker {
        margin-right: 20px
    }

    .tagify[disabled] {
        background: #F6F1E9 !important;
        opacity: 1;
        filter: none;
    }

    thead.position-sticky {
        position: sticky;
        top: 0;
        background: white;
        z-index: 6;
    }

</style>
{% endif %}
<!-- Drawer auto-opening script for association redirects -->
<script>
// Auto-open drawer when redirected from association save
(function() {
    var url = new URL(window.location.href);
    var params = new URLSearchParams(url.search);
    var id = params.get('id');
    var tab = params.get('tab');
    var target = params.get('target');
    
    if (!target || !id) return;
    
    function attemptDrawerOpening() {
        // Wait for jQuery to be available
        if (typeof jQuery === 'undefined') {
            setTimeout(attemptDrawerOpening, 500);
            return;
        }
        
        var $ = jQuery;
        var element = null;
        
        switch (target) {
            case 'contacts':
                element = tab == 'activity' ? $(`.contact_${id}_activity`)[0] : $(`.contact_${id}`)[0];
                break;
            case 'company':
                element = tab == 'activity' ? $(`.company_${id}_activity`)[0] : $(`.company_${id}`)[0];
                break;
            default:
                return;
        }
        
        if (element) {
            element.click();
        } else {
            // Retry once after 2 seconds in case elements are still loading
            setTimeout(function() {
                var retryElement = target === 'contacts' ? $(`.contact_${id}`)[0] : $(`.company_${id}`)[0];
                if (retryElement) retryElement.click();
            }, 2000);
        }
    }
    
    setTimeout(attemptDrawerOpening, 1000);
})();

</script>

{% endblock %}

{% block js %}

    <script>
        // Helper function to load scripts dynamically
        function loadScript(url, callback) {
            var script = document.createElement('script');
            script.type = 'text/javascript';
            script.src = url;
            script.onload = callback;
            script.onerror = function() {
                console.error('Failed to load script:', url);
            };
            document.head.appendChild(script);
        }

        // Check if jQuery is loaded, if not, load it
        function ensureJQuery(callback) {
            if (typeof window.jQuery === 'undefined') {
                console.log('jQuery not loaded, loading now...');
                loadScript('https://cdn.jsdelivr.net/jquery/latest/jquery.min.js', function() {
                    console.log('jQuery loaded successfully');
                    if (callback) callback();
                });
            } else {
                console.log('jQuery already loaded');
                if (callback) callback();
            }
        }

        // Check if DataTable is loaded, if not, load it
        function ensureDataTableScripts(callback) {
            if (typeof $.fn.DataTable === 'undefined') {
                console.log('DataTable not loaded, loading scripts...');
                loadScript('https://cdn.datatables.net/1.13.7/js/jquery.dataTables.min.js', function() {
                    console.log('DataTable core loaded');
                    loadScript('https://cdn.datatables.net/fixedcolumns/4.3.0/js/dataTables.fixedColumns.min.js', function() {
                        console.log('DataTable FixedColumns loaded');
                        if (callback) callback();
                    });
                });
            } else {
                console.log('DataTable already loaded');
                if (callback) callback();
            }
        }

        document.addEventListener('DOMContentLoaded', function() {
            var table = null;
            var requestNum = {% if contacts|length == 0 %}1{% else %}{{contacts|length}}{% endif %}
            var dataTableInitialized = false;
            var maxRetries = 5;
            var retryCount = 0;
            var retryInterval = 500; // ms

            // Make sure jQuery and DataTables are properly initialized
            function ensureDataTable(callback) {
                // First ensure jQuery is loaded
                ensureJQuery(function() {
                    // Then ensure DataTable scripts are loaded
                    ensureDataTableScripts(function() {
                        if (callback && typeof callback === 'function') {
                            callback();
                        }
                        dataTableInitialized = true;
                    });
                });
                return true;
            }

            function initializeDataTable() {
                if (!$("#contacts-table").length) {
                    console.log("Table element not found");
                    return false;
                }

                try {
                    console.log("🔍 DEBUG: Initializing DataTable...");

                    // Check if table has consistent structure
                    var headerCols = $("#contacts-table thead tr th").length;
                    var firstRowCols = $("#contacts-table tbody tr:first td").length;
                    console.log("🔍 DEBUG: Header columns:", headerCols, "First row columns:", firstRowCols);

                    if (headerCols > 0 && firstRowCols > 0 && headerCols !== firstRowCols) {
                        console.warn("🔍 DEBUG: Column count mismatch detected! Header:", headerCols, "Row:", firstRowCols);
                        // Try to fix by adding missing cells or adjusting colspan
                        $("#contacts-table tbody tr").each(function() {
                            var rowCols = $(this).find('td').length;
                            if (rowCols < headerCols) {
                                console.log("🔍 DEBUG: Fixing row with", rowCols, "columns");
                                // Add empty cells to match header count
                                for (var i = rowCols; i < headerCols; i++) {
                                    $(this).append('<td></td>');
                                }
                            }
                        });
                    }

                    table = $("#contacts-table").DataTable({
                        scrollX: true,
                        scrollY: "75vh",
                        scrollCollapse: true,
                        fixedColumns:   {
                            left: 2
                        },
                        ordering: false,
                        searching: false,  // Hide the search bar
                        paging: false,      // Hide pagination
                        info: false,        // Hide the information text
                        language: {
                            emptyTable: "{% translate_lang 'No data available in table' LANGUAGE_CODE %}"
                        },
                        // Add error handling
                        initComplete: function() {
                            console.log("🔍 DEBUG: DataTable initialization completed successfully");
                        },
                        drawCallback: function() {
                            console.log("🔍 DEBUG: DataTable draw completed");
                        }
                    });
                    console.log("🔍 DEBUG: DataTable initialized successfully");

                    // Debug FixedColumns functionality
                    setTimeout(function() {
                        console.log("🔍 DEBUG: Checking FixedColumns functionality...");
                        console.log("🔍 DEBUG: Table object:", table);
                        console.log("🔍 DEBUG: FixedColumns extension available?", $.fn.dataTable.FixedColumns !== undefined);
                        console.log("🔍 DEBUG: Table wrapper classes:", $("#contacts-table_wrapper").attr('class'));
                        console.log("🔍 DEBUG: Table classes:", $("#contacts-table").attr('class'));
                        
                        // Check if FixedColumns is actually applied
                        var fixedColumnsInstance = table.fixedColumns();
                        if (fixedColumnsInstance) {
                            console.log("🔍 DEBUG: FixedColumns instance:", fixedColumnsInstance);
                        } else {
                            console.log("🔍 DEBUG: FixedColumns instance not found!");
                        }
                        
                        // Check for fixed column classes
                        var fixedCells = $("#contacts-table .dtfc-fixed-left");
                        console.log("🔍 DEBUG: Fixed column cells found:", fixedCells.length);
                        if (fixedCells.length > 0) {
                            console.log("🔍 DEBUG: First fixed cell styles:", fixedCells.first().attr('style'));
                        }
                        
                        // Keep position-sticky class - DataTables needs it for proper header behavior
                        console.log("🔍 DEBUG: Keeping position-sticky class on thead for DataTables compatibility");

                        // Force CSS-based sticky positioning as fallback
                        setTimeout(function() {
                            // Check if DataTables FixedColumns is working
                            var fixedLeftCells = $("#contacts-table .dtfc-fixed-left");
                            console.log("🔍 DEBUG: Found", fixedLeftCells.length, "DataTables fixed left cells");

                            if (fixedLeftCells.length === 0) {
                                console.log("🔍 DEBUG: DataTables FixedColumns not working, CSS fallback will handle it");
                                // Our CSS sticky positioning will take over
                            } else {
                                console.log("🔍 DEBUG: DataTables FixedColumns is working properly");
                                // Remove any conflicting CSS
                                $("#contacts-table_wrapper .dataTables_scrollBody table tbody tr td:nth-child(1), #contacts-table_wrapper .dataTables_scrollBody table tbody tr td:nth-child(2)").css('position', '');
                            }

                            // Ensure proper styling regardless
                            $("#contacts-table th:nth-child(1), #contacts-table td:nth-child(1)").css('background-color', 'white');
                            $("#contacts-table th:nth-child(2), #contacts-table td:nth-child(2)").css('background-color', 'white');
                        }, 200);
                        
                        console.log("🔍 DEBUG: Visual debugging applied - red border on column 1, green on column 2");
                        
                        // Create debug panel
                        var debugPanel = $('<div class="debug-fixed-columns"></div>');
                        $('body').append(debugPanel);
                        
                        function updateDebugPanel() {
                            var info = [];
                            info.push("FixedColumns Debug:");
                            info.push("Extension available: " + ($.fn.dataTable.FixedColumns !== undefined));
                            info.push("Table wrapper class: " + ($("#contacts-table_wrapper").attr('class') || 'none'));
                            info.push("Fixed cells count: " + $("#contacts-table .dtfc-fixed-left").length);
                            info.push("Scroll position: " + $("#contacts-table_wrapper .dataTables_scrollBody").scrollLeft());
                            info.push("First col computed style:");
                            var firstCol = $("#contacts-table td:nth-child(1)").first();
                            if (firstCol.length) {
                                var computedStyle = window.getComputedStyle(firstCol[0]);
                                info.push("  position: " + computedStyle.position);
                                info.push("  left: " + computedStyle.left);
                                info.push("  z-index: " + computedStyle.zIndex);
                            }
                            debugPanel.html('<pre>' + info.join('\n') + '</pre>');
                        }
                        
                        updateDebugPanel();
                        setInterval(updateDebugPanel, 1000);
                        
                        // Monitor scroll events
                        $("#contacts-table_wrapper .dataTables_scrollBody").on('scroll', function() {
                            console.log("🔍 DEBUG: Scroll event - scrollLeft:", $(this).scrollLeft());
                            updateDebugPanel();
                        });
                        
                        // Fix positioning: Calculate proper left position for second column
                        function fixColumnPositions() {
                            var firstColWidth = $("#contacts-table th:nth-child(1)").outerWidth();
                            console.log("🔍 DEBUG: First column width:", firstColWidth);
                            
                            // Only update position if it's significantly different from CSS value (50px)
                            // This prevents constant movement during scroll
                            if (Math.abs(firstColWidth - 50) > 5) {
                                console.log("🔍 DEBUG: Updating second column position to:", firstColWidth);
                                $("#contacts-table th:nth-child(2), #contacts-table td:nth-child(2)").css({
                                    'left': firstColWidth + 'px'
                                });
                            } else {
                                console.log("🔍 DEBUG: Keeping CSS position (50px) - measured width is close:", firstColWidth);
                            }
                            
                            // If using FixedColumns, update the CSS variable
                            document.documentElement.style.setProperty('--dtfc-left-1', firstColWidth + 'px');
                        }
                        
                        fixColumnPositions();
                        
                        // Re-fix positions after table draw
                        table.on('draw.dt', function() {
                            setTimeout(fixColumnPositions, 100);
                        });
                        
                        // Handle dropdown z-index conflicts with sticky columns
                        function handleDropdownZIndex() {
                            // When dropdown shows, temporarily reduce sticky column z-index
                            $(document).on('show.bs.dropdown', function(e) {
                                console.log("🔍 DEBUG: Dropdown showing, reducing sticky column z-index");
                                $('#contacts-table th:nth-child(1), #contacts-table td:nth-child(1)').css('z-index', '1');
                                $('#contacts-table th:nth-child(2), #contacts-table td:nth-child(2)').css('z-index', '1');

                                // Force dropdown to highest z-index with multiple attempts
                                setTimeout(function() {
                                    $('.dropdown-menu.show').each(function() {
                                        $(this).css({
                                            'z-index': '9999 !important',
                                            'position': 'absolute'
                                        });
                                        // Also set via style attribute to override any CSS
                                        this.style.setProperty('z-index', '9999', 'important');
                                    });
                                }, 10);

                                // Additional attempt after Popper.js positioning
                                setTimeout(function() {
                                    $('.dropdown-menu.show').each(function() {
                                        this.style.setProperty('z-index', '9999', 'important');
                                    });
                                }, 50);
                            });

                            // When dropdown hides, restore sticky column z-index
                            $(document).on('hide.bs.dropdown', function(e) {
                                console.log("🔍 DEBUG: Dropdown hiding, restoring sticky column z-index");
                                setTimeout(function() {
                                    $('#contacts-table th:nth-child(1), #contacts-table td:nth-child(1)').css('z-index', '5');
                                    $('#contacts-table th:nth-child(2), #contacts-table td:nth-child(2)').css('z-index', '4');
                                }, 100);
                            });
                        }
                        
                        handleDropdownZIndex();
                    }, 1000);

                    if (table) {
                        // Only proceed with dropdown setup if we have a table
                        try {
                            var dropdownNodes = table.cells(null, 1).nodes();
                            if (dropdownNodes && dropdownNodes.length > 0) {
                                const dropdowns = $('.dropdown-toggle', dropdownNodes);
                                if (dropdowns && dropdowns.length > 0) {
                                    dropdowns.each(function(index, dropdownToggleEl) {
                                        if (dropdownToggleEl && typeof bootstrap !== 'undefined' && bootstrap.Dropdown) {
                                            try {
                                                var instance = new bootstrap.Dropdown(dropdownToggleEl, {
                                                    popperConfig: function(defaultBsPopperConfig) {
                                                        return { ...defaultBsPopperConfig, strategy: "fixed" };
                                                    },
                                                });

                                                if (dropdownToggleEl.addEventListener) {
                                                    dropdownToggleEl.addEventListener("show.bs.dropdown", function (event) {
                                                        $(event.target).closest("td").addClass("z-index-3");
                                                    });

                                                    dropdownToggleEl.addEventListener("hide.bs.dropdown", function (event) {
                                                        $(event.target).closest("td").removeClass("z-index-3");
                                                    });
                                                }
                                            } catch (dropdownError) {
                                                console.log("Error setting up dropdown:", dropdownError);
                                            }
                                        }
                                    });
                                }
                            }
                        } catch (tableError) {
                            console.log("Error setting up table dropdowns:", tableError);
                        }

                        // Initialize view dropdown with body attachment to avoid container clipping
                        try {
                            const viewDropdownToggle = document.querySelector('.nav-item .dropdown-toggle[data-bs-toggle="dropdown"]');
                            if (viewDropdownToggle && typeof bootstrap !== 'undefined' && bootstrap.Dropdown) {
                                console.log("🔍 DEBUG: Initializing view dropdown with body attachment");
                                const viewDropdownInstance = new bootstrap.Dropdown(viewDropdownToggle, {
                                    popperConfig: function(defaultBsPopperConfig) {
                                        return {
                                            ...defaultBsPopperConfig,
                                            strategy: "absolute",
                                            modifiers: [
                                                ...defaultBsPopperConfig.modifiers,
                                                {
                                                    name: 'preventOverflow',
                                                    options: {
                                                        boundary: 'viewport'
                                                    }
                                                },
                                                {
                                                    name: 'flip',
                                                    options: {
                                                        boundary: 'viewport'
                                                    }
                                                }
                                            ]
                                        };
                                    },
                                });

                                viewDropdownToggle.addEventListener("show.bs.dropdown", function (event) {
                                    console.log("🔍 DEBUG: View dropdown showing, preparing z-index");
                                    // Reduce sticky column z-index temporarily
                                    $('#contacts-table th:nth-child(1), #contacts-table td:nth-child(1)').css('z-index', '1');
                                    $('#contacts-table th:nth-child(2), #contacts-table td:nth-child(2)').css('z-index', '1');
                                });

                                viewDropdownToggle.addEventListener("shown.bs.dropdown", function (event) {
                                    console.log("🔍 DEBUG: View dropdown shown, applying z-index fix");
                                    const dropdownMenu = event.target.nextElementSibling;
                                    if (dropdownMenu) {
                                        dropdownMenu.style.setProperty('z-index', '9999', 'important');
                                    }
                                });

                                viewDropdownToggle.addEventListener("hide.bs.dropdown", function (event) {
                                    console.log("🔍 DEBUG: View dropdown hiding, restoring sticky column z-index");
                                    // Restore sticky column z-index
                                    setTimeout(function() {
                                        $('#contacts-table th:nth-child(1), #contacts-table td:nth-child(1)').css('z-index', '5');
                                        $('#contacts-table th:nth-child(2), #contacts-table td:nth-child(2)').css('z-index', '4');
                                    }, 100);
                                });
                            }
                        } catch (viewDropdownError) {
                            console.log("Error setting up view dropdown:", viewDropdownError);
                        }
                    }
                    return true;
                } catch (e) {
                    console.error("Error initializing DataTable:", e);
                    // Even if DataTable fails, still try to run drawer opening script
                    setTimeout(function() {
                        console.log("🔍 DEBUG: Running drawer script despite DataTable error...");
                        try {
                            // Add debug version of open-drawer.js with extensive logging
                            console.log("🔍 DEBUG: About to run drawer opening script (fallback)");
                            var sourceUrl = window.location.href;
                            console.log("🔍 DEBUG: sourceUrl:", sourceUrl);

                            var url = new URL(sourceUrl);
                            var params = new URLSearchParams(url.search);
                            var id = params.get('id');
                            var tab = params.get('tab');
                            var target = params.get('target');

                            console.log("🔍 DEBUG: URL params:", {
                                id: id,
                                tab: tab,
                                target: target
                            });

                            if (!target || !id) {
                                console.log("🔍 DEBUG: No target or id found in URL, skipping drawer opening");
                                return;
                            }

                            setTimeout(function () {
                                console.log("🔍 DEBUG: Running drawer opening logic after timeout (fallback)");
                                var element = null;
                                
                                switch (target) {
                                    case 'contacts':
                                        console.log("🔍 DEBUG: Looking for contact element with selector: .contact_" + id);
                                        if (tab == 'activity') {
                                            element = $(`.contact_${id}_activity`)[0];
                                        } else {
                                            element = $(`.contact_${id}`)[0];
                                        }
                                        break;
                                    case 'company':
                                        console.log("🔍 DEBUG: Looking for company element with selector: .company_" + id);
                                        if (tab == 'activity') {
                                            element = $(`.company_${id}_activity`)[0];
                                        } else {
                                            element = $(`.company_${id}`)[0];
                                        }
                                        break;
                                    default:
                                        console.log("🔍 DEBUG: Unknown target type:", target);
                                        return;
                                }

                                console.log("🔍 DEBUG: Final element found (fallback):", element);
                                
                                if (element) {
                                    console.log("🔍 DEBUG: Element exists, attempting to click (fallback)");
                                    element.click();
                                    console.log("🔍 DEBUG: ✅ Element clicked successfully (fallback)");
                                } else {
                                    console.log("🔍 DEBUG: ❌ Element not found (fallback)");
                                }
                            }, 100);
                        } catch (fallbackError) {
                            console.error("Error in fallback drawer script:", fallbackError);
                        }
                    }, 1000);
                    return false;
                }
            }

            // Initialize DataTable immediately on page load
            ensureDataTable(function() {
                {% if contacts|length == 0 %}
                // If no contacts, initialize table right away
                initializeDataTable();
                // Also initialize the drawer functionality
                setTimeout(function() {
                    // Add debug version for empty contacts case too
                    console.log("🔍 DEBUG: About to run drawer opening script (empty contacts case)");
                    var sourceUrl = window.location.href;
                    console.log("🔍 DEBUG: sourceUrl:", sourceUrl);

                    var url = new URL(sourceUrl);
                    var params = new URLSearchParams(url.search);
                    var id = params.get('id');
                    var tab = params.get('tab');
                    var target = params.get('target');

                    console.log("🔍 DEBUG: URL params:", {
                        id: id,
                        tab: tab,
                        target: target
                    });

                    if (!target || !id) {
                        console.log("🔍 DEBUG: No target or id found in URL, skipping drawer opening");
                        return;
                    }

                    setTimeout(function () {
                        console.log("🔍 DEBUG: Running drawer opening logic after timeout (empty case)");
                        var element = null;
                        
                        switch (target) {
                            case 'contacts':
                                console.log("🔍 DEBUG: Looking for contact element with selector: .contact_" + id);
                                if (tab == 'activity') {
                                    element = $(`.contact_${id}_activity`)[0];
                                } else {
                                    element = $(`.contact_${id}`)[0];
                                }
                                break;
                            case 'company':
                                console.log("🔍 DEBUG: Looking for company element with selector: .company_" + id);
                                if (tab == 'activity') {
                                    element = $(`.company_${id}_activity`)[0];
                                } else {
                                    element = $(`.company_${id}`)[0];
                                }
                                break;
                            default:
                                console.log("🔍 DEBUG: Unknown target type:", target);
                                return;
                        }

                        console.log("🔍 DEBUG: Final element found:", element);
                        
                        if (element) {
                            console.log("🔍 DEBUG: Element exists, attempting to click");
                            element.click();
                            console.log("🔍 DEBUG: ✅ Element clicked successfully");
                        } else {
                            console.log("🔍 DEBUG: ❌ Element not found (empty case)");
                        }
                    }, 100);
                }, 500);
                {% else %}
                // Render DataTable after htmx completed
                document.addEventListener('htmx:afterRequest', function(evt) {
                    if (requestNum <= 0 || dataTableInitialized) return;

                    if (evt.target && evt.target.tagName && evt.target.tagName.toLowerCase() === 'tr') {
                        requestNum -= 1;
                        if (requestNum <= 0) {
                            // Use a timeout to ensure all scripts and DOM are fully loaded
                            setTimeout(function() {
                                if (initializeDataTable()) {
                                    // Additional initialization that should run after table is created
                                    try {
                                        // Add debug version of open-drawer.js with extensive logging
                                        console.log("🔍 DEBUG: About to run drawer opening script");
                                        var sourceUrl = window.location.href;
                                        console.log("🔍 DEBUG: sourceUrl:", sourceUrl);

                                        var url = new URL(sourceUrl);
                                        var params = new URLSearchParams(url.search);
                                        var id = params.get('id');
                                        var tab = params.get('tab');
                                        var target = params.get('target');

                                        console.log("🔍 DEBUG: URL params:", {
                                            id: id,
                                            tab: tab,
                                            target: target
                                        });

                                        if (!target || !id) {
                                            console.log("🔍 DEBUG: No target or id found in URL, skipping drawer opening");
                                            return;
                                        }

                                        setTimeout(function () {
                                            console.log("🔍 DEBUG: Running drawer opening logic after timeout");
                                            var element = null;
                                            
                                            switch (target) {
                                                case 'contacts':
                                                    console.log("🔍 DEBUG: Looking for contact element with selector: .contact_" + id);
                                                    if (tab == 'activity') {
                                                        element = $(`.contact_${id}_activity`)[0];
                                                        console.log("🔍 DEBUG: Activity tab - element found:", element);
                                                    } else {
                                                        element = $(`.contact_${id}`)[0];
                                                        console.log("🔍 DEBUG: Regular tab - element found:", element);
                                                    }
                                                    break;
                                                case 'company':
                                                    console.log("🔍 DEBUG: Looking for company element with selector: .company_" + id);
                                                    if (tab == 'activity') {
                                                        element = $(`.company_${id}_activity`)[0];
                                                    } else {
                                                        element = $(`.company_${id}`)[0];
                                                    }
                                                    break;
                                                default:
                                                    console.log("🔍 DEBUG: Unknown target type:", target);
                                                    return;
                                            }

                                            console.log("🔍 DEBUG: Final element found:", element);
                                            
                                            if (element) {
                                                console.log("🔍 DEBUG: Element exists, attempting to click");
                                                console.log("🔍 DEBUG: Element classes:", element.className);
                                                console.log("🔍 DEBUG: Element hx-get:", $(element).attr('hx-get'));
                                                console.log("🔍 DEBUG: Element hx-target:", $(element).attr('hx-target'));
                                                
                                                element.click();
                                                console.log("🔍 DEBUG: ✅ Element clicked successfully");
                                                                                         } else {
                                                 console.log("🔍 DEBUG: ❌ Element not found, trying again in 2 seconds...");
                                                 console.log("🔍 DEBUG: Available contact elements on page:");
                                                 $('[class*="contact_"]').each(function(i, el) {
                                                     console.log("🔍 DEBUG: - Found element:", el.className);
                                                 });
                                                 
                                                 // Retry after 2 seconds in case the element is still loading
                                                 setTimeout(function() {
                                                     console.log("🔍 DEBUG: Retrying after 2 seconds...");
                                                     var retryElement = null;
                                                     if (target === 'contacts') {
                                                         retryElement = $(`.contact_${id}`)[0];
                                                     } else if (target === 'company') {
                                                         retryElement = $(`.company_${id}`)[0];
                                                     }
                                                     
                                                     if (retryElement) {
                                                         console.log("🔍 DEBUG: ✅ Element found on retry, clicking now");
                                                         retryElement.click();
                                                     } else {
                                                         console.log("🔍 DEBUG: ❌ Element still not found after retry");
                                                         console.log("🔍 DEBUG: Final check - all elements with contact class:");
                                                         $('[class*="contact_"]').each(function(i, el) {
                                                             console.log("🔍 DEBUG: - Element " + i + ":", el.className, el);
                                                         });
                                                     }
                                                 }, 2000);
                                             }
                                        }, 100);
                                    } catch (e) {
                                        console.error("Error in drawer initialization:", e);
                                    }
                                }
                            }, 500);
                        }
                    }
                });
                {% endif %}
            });

            function toggleText() {
                var x = document.getElementById("select-additional-options-toggle");
                if (x) {
                    var contacts_select_info = document.getElementById("contacts-selects-information");
                    var toggle_data = x.getAttribute('toggle-data')
                    if (toggle_data !== "true") {
                        {% if LANGUAGE_CODE == 'ja'%}
                        x.innerHTML = "選択を解除";
                        contacts_select_info.innerHTML = "すべての連絡先 ({{paginator.count}}) が選択されています. ";
                        {% else %}
                        x.innerHTML = "Clear All";
                        contacts_select_info.innerHTML = "All contacts ({{paginator.count}}) are selected. ";
                        {% endif %}

                        $(".flag_all").each(function(index, element) {
                            element.value = true
                        });

                        x.setAttribute('toggle-data',"true")

                    } else {
                        x.setAttribute('toggle-data',"false")

                        var addcontactelem = document.getElementById("update-contacts");
                        if (addcontactelem) {
                            addcontactelem.classList.add("disabled");
                        }

                        var downloadelem = document.getElementById("csv_download-contacts");
                        if (downloadelem) {
                            downloadelem.classList.add("disabled");
                        }

                        $('input[type=checkbox]').prop('checked', false);

                        //Hide
                        var element_select_options = document.getElementById("d-select-additional-options");
                        if (element_select_options) {
                            element_select_options.classList.add("d-none");
                        }

                        x.innerHTML = "{% if LANGUAGE_CODE == 'ja'%}すべて選択 ({{paginator.count}}) このセクションの連絡先{%else%}Select All ({{paginator.count}}) contacts in this sections{%endif%}";

                        {% if LANGUAGE_CODE == 'ja'%}
                            contacts_select_info.innerHTML = "すべての連絡先 ({{paginator_item_end}}) このページで選択されています。";
                        {% else %}
                            contacts_select_info.innerHTML = "All contacts ({{paginator_item_end}}) on this page are selected. ";
                        {% endif %}

                        $(".flag_all").each(function(index, element) {
                            element.value = false
                        });
                    }
                }
            }

            // Make toggleText function globally available
            window.toggleText = toggleText;

            var count_checked = 0;
            function select_all() {
                if ($('#selectAll').length > 0) {
                    $('#selectAll').prop('checked', !$('#selectAll').prop('checked'));
                    if ($('#selectAll').prop('checked') == true) {
                        var addcontactelem = document.getElementById("update-contacts");
                        if (addcontactelem) {
                            addcontactelem.classList.add("disabled");
                        }

                        var downloadelem = document.getElementById("csv_download-contacts");
                        if (downloadelem) {
                            downloadelem.classList.add("disabled");
                        }

                        $('input[type=checkbox]').prop('checked', false);

                        var element_select_options = document.getElementById("d-select-additional-options");
                        if (element_select_options) {
                            element_select_options.classList.add("d-none");
                        }

                    } else {
                        var addcontactelem = document.getElementById("update-contacts");
                        if (addcontactelem) {
                            addcontactelem.classList.remove("disabled");
                        }

                        var downloadelem = document.getElementById("csv_download-contacts");
                        if (downloadelem) {
                            downloadelem.classList.remove("disabled");
                        }

                        $('input[type=checkbox]').prop('checked', true);

                        var element_select_options = document.getElementById("d-select-additional-options");
                        if (element_select_options) {
                            element_select_options.classList.remove("d-none");
                        }

                        var x = document.getElementById("select-additional-options-toggle");
                        if (x) {
                            x.innerHTML = "{% if LANGUAGE_CODE == 'ja'%}すべて ({{paginator.count}}) の連絡先を選択{%else%}Select All ({{paginator.count}}) contacts{%endif%}";
                            x.setAttribute('toggle-data',"false");
                        }

                        $(".flag_all").each(function(index, element) {
                            element.value = false
                        });
                    }
                }
            }

            // Make select_all function globally available
            window.select_all = select_all;
        });
    </script>

    {% comment %} Custom search functionality with proper error handling {% endcomment %}
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Debug: Add HTMX event listeners for debugging
            document.body.addEventListener('htmx:beforeRequest', function(evt) {
                if (evt.target.id && evt.target.id.startsWith('row-')) {
                    console.log('🔍 DEBUG: HTMX loading contact row:', evt.target.id);
                }
            });

            document.body.addEventListener('htmx:afterRequest', function(evt) {
                if (evt.target.id && evt.target.id.startsWith('row-')) {
                    if (evt.detail.xhr.status !== 200) {
                        console.error('🔍 DEBUG: HTMX request failed:', evt.detail.xhr.responseText);
                    }
                }
            });

            document.body.addEventListener('htmx:responseError', function(evt) {
                console.error('🔍 DEBUG: HTMX response error:', evt.detail);
            });

            // Make functions globally available for search
            window.toggleSearch = function() {
                const searchWrapper = document.querySelector('.search-wrapper');
                if (!searchWrapper) {
                    console.log("Search wrapper not found");
                    return;
                }

                searchWrapper.classList.toggle('expanded');
                searchWrapper.classList.toggle('hover-tooltip');

                const toolTipText = searchWrapper.querySelector('.search-wrapper-tooltip');
                if (toolTipText) {
                    toolTipText.classList.toggle('d-none');
                }

                const searchInput = document.getElementById('base-search-input');
                if (searchInput) {
                    searchInput.focus();
                }
            };

            window.toolTipCheck = function() {
                const searchWrapper = document.querySelector('.search-wrapper');
                if (!searchWrapper) {
                    console.log("Search wrapper not found");
                    return;
                }

                searchWrapper.classList.toggle('hover-tooltip');

                const toolTipText = searchWrapper.querySelector('.search-wrapper-tooltip');
                if (toolTipText) {
                    toolTipText.classList.toggle('d-none');
                }
            };

            window.openSearch = function() {
                const search_bar = document.getElementById('search-bar');
                if (!search_bar) {
                    console.log("Search bar not found");
                    return;
                }

                search_bar.classList.toggle('max-md:tw-flex');
                search_bar.classList.toggle('max-md:tw-hidden');

                const searchInput = document.getElementById('base-search-input-mobile');
                if (searchInput) {
                    searchInput.focus();
                }
            };

            {% if search_q %}
            // Use a timeout to ensure DOM is ready
            setTimeout(function() {
                if (typeof window.toolTipCheck === 'function') {
                    window.toolTipCheck();
                }
            }, 300);
            {% endif %}
        });
    </script>

    {% include 'data/javascript/toggleSearch.html' %}

    <script>
        // Keyboard shortcut for opening the 'Create New' drawer (n key)
        document.addEventListener('keydown', function(event) {
            // Ignore if input, textarea, or contenteditable is focused
            const active = document.activeElement;
            if (active && (active.tagName === 'INPUT' || active.tagName === 'TEXTAREA' || active.isContentEditable)) {
                return;
            }

            // Check if the key pressed is 'n'
            if (event.key.toLowerCase() === 'n') {
                event.preventDefault(); // Prevent default 'n' behavior
                
                // Find the 'Create New' button (adjust selector if needed)
                const newButton = document.querySelector('.customer-create-wizard.view_form_trigger'); 
                
                if (newButton) {
                    newButton.click(); // Simulate click to open the drawer
                }
            }
        });


        
    </script>
     <script>
        function getSelectedContacts() {
            var selectedOrders = [];
            var classNameElements = document.getElementsByClassName("contact-selection");
            
            if (classNameElements){
                classNameElements.forEach(function(classNameElement) {
                    if (classNameElement.checked) {
                        selectedOrders.push(classNameElement.value);
                    }
                });  
            }
            return selectedOrders;
        }
    </script>

    <script>
        function check_permission_action(event, permission_type, ...args){

            let source = args.length > 0 ? args[0] : null;

            const checkInputs = document.querySelectorAll('.check_input:checked');

            let members = "{{group_members}}"
            members = members.split(',')
            const user_id = '{{request.user.id}}'
            const permission = '{{permission}}';
            const permission_list = permission.split('|');
            let scope = ''
            permission_list.forEach(p => {
                if (p.includes(permission_type)) {
                    p_split = p.split('_');
                    scope = p_split[0]
                }
            })

            let msg = '';
            let denied = false;
            for (let i = 0; i < checkInputs.length; i++) {
                var owner_id = checkInputs[i].dataset.owner;
                if (owner_id){
                    if (scope == 'user'){
                        if (owner_id.toString() !== user_id.toString()) {
                            {% if LANGUAGE_CODE == 'ja' %}
                            msg = "操作が拒否されました。自分のアイテムのみ編集または削除することができます。";
                            {% else %}
                            msg = "Action denied. You are only allowed to edit or delete your own items.";
                            {% endif %}                    
                            checkInputs[i].click()
                            denied = true;
                        }
                    } else if (scope == 'team'){
                        if (!members.includes(owner_id.toString())) {
                            {% if LANGUAGE_CODE == 'ja' %}
                            msg = "操作が拒否されました。自分または自分のチームに割り当てられたアイテムのみ編集または削除できます。";
                            {% else %}
                            msg = "Action denied. You can only edit or delete items assigned to you or your team.";
                            {% endif %}
                            checkInputs[i].click()
                            denied = true;
                        }
                    } 
                }
            }
            if (denied) {
                event.preventDefault();
                event.stopImmediatePropagation();
                document.getElementById('permissionActionWarning').innerHTML = msg;
                setTimeout(() => {
                    document.getElementById('permissionActionWarning').innerHTML = '';
                }, 4000);
                msg = ''
            } else if (source){
                const modalEl = document.getElementById(source);
                const modal = bootstrap.Modal.getOrCreateInstance(modalEl);
                modal.show();
            }

        }
    </script>
{% endblock %}
