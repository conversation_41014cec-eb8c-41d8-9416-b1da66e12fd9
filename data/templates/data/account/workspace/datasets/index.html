{% extends 'base.html' %}
{% load i18n %}
{% load hosts %}
{% load custom_tags %}
{% get_current_language as LANGUAGE_CODE %}
{% block content %}

<div class="w-100" id="reports-table">
    <div class="mb-15 gx-5">
        <div class="d-flex align-items-end mb-2 align-items-center" style="background-color: #FAFAFA !important;">
            <div class="w-50 p-10 position-sticky top-0 z-index-11 bg-white tw-overflow-x-auto tw-whitespace-nowrap d-flex justify-content-between align-items-center" style="background-color: #FAFAFA !important; z-index: 10;">
                <div class="d-flex tw-whitespace-nowrap" style="scrollbar-width: thin; overflow-x: none; white-space: nowrap; overflow-y: none;">
                    <div class="tw-text-title-header-object tw-leading-[150%] tw-text-[#8F8E93]">
                        {% if LANGUAGE_CODE == 'ja' %}
                        データセット
                        {% else %}
                        Datasets
                        {% endif %}
                    </div>
                </div>
            </div>
            <div class="w-50 d-flex justify-content-end tw-mr-5" id="view-container">
                <div class="{% include "data/utility/table-button.html" %} btn-group tw-h-[32px]">
                    <button id="channel_create_button" class="tw-w-[100px] align-items-center d-flex btn btn-primary btn-md py-1 dataset-create-button"
                    hx-get="{% url 'dataset_form' %}" 
                    hx-trigger="click"
                    hx-target="#dataset-drawer-content"
                    hx-indicator=".loading-drawer-spinner,.view-form"
                    >
                        <span class="svg-icon svg-icon-2">
                            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <rect opacity="0.3" x="2" y="2" width="20" height="20" rx="10" fill="currentColor"/>
                                <rect x="10.8891" y="17.8033" width="12" height="2" rx="1" transform="rotate(-90 10.8891 17.8033)" fill="currentColor"/>
                                <rect x="6.01041" y="10.9247" width="12" height="2" rx="1" fill="currentColor"/>
                            </svg>
                        </span>
                
                        {% if LANGUAGE_CODE == 'ja'%}
                        新規
                        {% else %}
                        New
                        {% endif %}
                    </button>
                </div>
            </div>
        </div>

        <div class="px-lg-10 px-5">
            {% include 'data/common/permission-action-warning-message.html' %}
            {% include 'data/account/workspace/datasets/view-menu.html' %}

            <div id="panels-table-container">
                {% if datasets %}
                <form method="POST" action="{% host_url 'dataset_form' host 'app' %}" id="dataset-form">
                    {% csrf_token %}
                    <input name="object_type" type="hidden" value="{{object_type}}">

                    <table class="{% include "data/utility/table.html" %} mb-0">
                        <thead class="{% include "data/utility/table-header.html" %}">
                            <tr class="border-bottom border-bottom-1">
                                <th class="fw-bolder w-10">
                                </th>
                                <th class="border-right-1">
                                    ID
                                </th>
                                <th class="">
                                    {% if LANGUAGE_CODE == 'ja' %}
                                    名前
                                    {% else %}
                                    Name
                                    {% endif %}
                                </th>
                                <th class="">
                                    {% if LANGUAGE_CODE == 'ja' %}
                                    オブジェクト
                                    {% else %}
                                    Objects
                                    {% endif %}
                                </th>
                                <th class="">
                                    {% if LANGUAGE_CODE == 'ja' %}
                                    編集者
                                    {% else %}
                                    Edited By
                                    {% endif %}
                                </th>
                            </tr>
                        </thead>

                        <tbody>
                            {% for obj in datasets %}
                                <tr id="row-{{obj.id}}">
                                    <td class="w-10">
                                        <input style="" id="{{obj.id}}" class="panel-selection form-check-input check_input" type="checkbox" name="dataset_ids" data-owner="{{obj.owner.user.id}}" value="{{obj.id}}" onclick="checking_checkbox(this)"/>
                                    </td>
                                    <td class="fw-bolder border-right-1" style="border-right: 1px solid !important; border-right-color: rgb(234, 234, 234) !important;">
                                        <div class="fw-bold py-2">
                                            {% if obj.is_deleted %}
                                            {{ obj.dataset_id|stringformat:"04d" }}
                                            {% else %}
                                            <a class="text-dark text-hover-primary fw-bolder"
                                                href="{% host_url 'dataset_view' dataset_id=obj.id host 'app' %}?module={{menu_key}}"
                                                >
                                                {{ obj.dataset_id|stringformat:"04d" }}
                                            </a>
                                            {% endif %}
                                        </div>
                                    </td>
                                    <td class="fw-bold">
                                        <div {% if obj.is_deleted %}class="blurry-text"{% else %}class="text-start"{%endif%}>
                                            {% if obj.is_deleted %}
                                            {{obj.name}}
                                            {% else %}
                                            <a class="text-dark text-hover-primary fw-bolder"
                                                href="{% host_url 'dataset_view' dataset_id=obj.id host 'app' %}?module={{menu_key}}"
                                                >
                                                {{obj.name}}
                                            </a>
                                            {% endif %}
                                        </div>
                                    </td>
                                    <td class="py-1">
                                        <div {% if obj.is_deleted %}class="blurry-text"{% else %}class="text-start"{%endif%}>
                                            {% for obj in obj.data_source|split:',' %}{% if not forloop.first %}, {% endif %}{{ obj|object_panel_type:request }}{% endfor %}
                                        </div>
                                    </td>
                                    <td class="py-1">
                                        {% if obj.is_deleted %}
                                            <div class="blurry-text"
                                                {{ obj.edited_by.first_name }}>
                                            </div>
                                        {% else %}
                                            <div class="text-start">
                                                {% if obj.edited_by %}
                                                    {% if obj.edited_by.verification.profile_photo %}
                                                    <img class="w-20px rounded-circle me-2" src="{{obj.edited_by.verification.profile_photo.url}}" style="object-fit: cover !important;aspect-ratio: 16 / 16;" />
                                                    {% else %}
                                                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                            <path d="M6.28548 15.0861C7.34369 13.1814 9.35142 12 11.5304 12H12.4696C14.6486 12 16.6563 13.1814 17.7145 15.0861L19.3493 18.0287C20.0899 19.3618 19.1259 21 17.601 21H6.39903C4.87406 21 3.91012 19.3618 4.65071 18.0287L6.28548 15.0861Z" fill="currentColor"/>
                                                            <rect opacity="0.3" x="8" y="3" width="8" height="8" rx="4" fill="currentColor"/>
                                                        </svg>    
                                                    {% endif %}
                                                    {{ obj.edited_by.first_name }}
                                                {% else %}
                                                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                        <path d="M6.28548 15.0861C7.34369 13.1814 9.35142 12 11.5304 12H12.4696C14.6486 12 16.6563 13.1814 17.7145 15.0861L19.3493 18.0287C20.0899 19.3618 19.1259 21 17.601 21H6.39903C4.87406 21 3.91012 19.3618 4.65071 18.0287L6.28548 15.0861Z" fill="currentColor"/>
                                                        <rect opacity="0.3" x="8" y="3" width="8" height="8" rx="4" fill="currentColor"/>
                                                    </svg>
                                                    Sanka
                                                {% endif %}
                                            </div>
                                        {%endif%}
                                    </td>
                                </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </form>

                {% if paginator and paginator.count > 0 %}
                <div class="{% include "data/utility/pagination.html" %}">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            {% if LANGUAGE_CODE == 'ja'%}
                            {{paginator_item_begin}}–{{paginator_item_end}} の {{paginator.count}} 件
                            {% else %}
                            Viewing {{paginator_item_begin}}–{{paginator_item_end}} of {{paginator.count}} results
                            {% endif %}
                        </div>
                        <div>
                            {% if panels.has_previous %}     
                                <a class="btn tw-bg-gray-200 hover:tw-bg-gray-300 w-100px" href="{% host_url 'load_object_page' menu_key object_type|object_type_slug:request host 'app' %}?page=1&{% query_transform %}">&laquo; {% if LANGUAGE_CODE == 'ja'%}最初 {% else %}First{% endif %}</a>
                                <a class="btn tw-bg-gray-200 hover:tw-bg-gray-300 w-100px" href="{% host_url 'load_object_page' menu_key object_type|object_type_slug:request host 'app' %}?page={{ panels.previous_page_number }}&{% query_transform %}">{% if LANGUAGE_CODE == 'ja'%}前 {% else %}Previous{% endif %}</a>
                            {% endif %}
                                    
                            {% if panels.has_next %}
                                <a class="btn tw-bg-gray-200 hover:tw-bg-gray-300 w-100px" href="{% host_url 'load_object_page' menu_key object_type|object_type_slug:request host 'app' %}?page={{ panels.next_page_number }}&{% query_transform %}">{% if LANGUAGE_CODE == 'ja'%}次 {% else %}Next{% endif %}</a>
                                <a class="btn tw-bg-gray-200 hover:tw-bg-gray-300 w-100px" href="{% host_url 'load_object_page' menu_key object_type|object_type_slug:request host 'app' %}?page={{ panels.paginator.num_pages }}&{% query_transform %}">{% if LANGUAGE_CODE == 'ja'%}最後 {% else %}Last{% endif %} &raquo;</a>
                            {% endif %}
                        </div>
                    </div>
                </div>
                {% endif %}
                {% else %}
                <div class="text-center py-10">
                    <div class="text-muted">
                        {% if LANGUAGE_CODE == 'ja' %}
                        データがありません
                        {% else %}
                        No data available
                        {% endif %}
                    </div>
                </div>
                {% endif %}
            </div>

        </div>
    </div>
</div>

<script>
    function toggleText() {
        var x = document.getElementById("select-additional-options-toggle");
        var toggle_data = x.getAttribute('toggle-data')
        if (toggle_data !== "true") {
            
            {% if LANGUAGE_CODE == 'ja'%}
            x.innerHTML = "選択を解除";
            {% else %}
            x.innerHTML = "Clear All";
            {% endif %}

          $(".flag_all").each(function(index, element) {
                element.value = true
            });

            x.setAttribute('toggle-data',"true")

        } else {

            x.setAttribute('toggle-data',"false")

            addcontactelem = document.getElementById("update-report");
            addcontactelem.classList.add("disabled");
 
            $('input[type=checkbox]').prop('checked', false);

            //Hide
            var element_select_options = document.getElementById("d-select-additional-options");
            element_select_options.classList.add("d-none")

            x.innerHTML = "{% if LANGUAGE_CODE == 'ja'%}すべて選択 ({{paginator.count}}) このセクションの連絡先{%else%}Select All ({{paginator.count}}) contacts in this sections{%endif%}";
            
            $(".flag_all").each(function(index, element) {
                element.value = false
            });

        }
      }

    const checking_checkbox = (elem,event) => {
        if (elem.checked) {
            document.getElementById('panel-bulk-action-container').classList.remove('d-none')
            document.getElementById('panel-view-container').classList.add('tw-hidden')
        } else {
            taskSelections = document.getElementsByClassName('panel-selection')
            for (let i = 0; i < taskSelections.length; i++) {
                const element = taskSelections[i];
                if (element.checked) {
                    return
                }
            }
            document.getElementById('panel-bulk-action-container').classList.add('d-none')
            document.getElementById('panel-view-container').classList.remove('tw-hidden')
        }
    }

    function check_permission_action(event, permission_type, ...args){
        let source = args.length > 0 ? args[0] : null;
        
        const checkInputs = document.querySelectorAll('.check_input:checked');

        let members = "{{group_members}}"
        members = members.split(',')
        const user_id = '{{request.user.id}}'
        const permission = '{{permission}}';
        const permission_list = permission.split('|');
        let scope = ''
        permission_list.forEach(p => {
            if (p.includes(permission_type)) {
                p_split = p.split('_');
                scope = p_split[0]
            }
        })

        let msg = '';
        let denied = false;
        for (let i = 0; i < checkInputs.length; i++) {
            var owner_id = checkInputs[i].dataset.owner;
            if (owner_id){
                if (scope == 'user'){
                    if (owner_id.toString() !== user_id.toString()) {
                        {% if LANGUAGE_CODE == 'ja' %}
                        msg = "操作が拒否されました。自分のアイテムのみ編集または削除することができます。";
                        {% else %}
                        msg = "Action denied. You are only allowed to edit or delete your own items.";
                        {% endif %}                    
                        checkInputs[i].click()
                        denied = true;
                    }
                } else if (scope == 'team'){
                    if (!members.includes(owner_id.toString())) {
                        {% if LANGUAGE_CODE == 'ja' %}
                        msg = "操作が拒否されました。自分または自分のチームに割り当てられたアイテムのみ編集または削除できます。";
                        {% else %}
                        msg = "Action denied. You can only edit or delete items assigned to you or your team.";
                        {% endif %}
                        checkInputs[i].click()
                        denied = true;
                    }
                } 
            }
        }
        if (denied) {
            event.preventDefault();
            event.stopImmediatePropagation();
            document.getElementById('permissionActionWarning').innerHTML = msg;
            setTimeout(() => {
                document.getElementById('permissionActionWarning').innerHTML = '';
            }, 4000);
            msg = ''
        } else if (source){
            const modalEl = document.getElementById(source);
            const modal = bootstrap.Modal.getOrCreateInstance(modalEl);
            modal.show();
        }
        
    }
</script>
{% endblock %}