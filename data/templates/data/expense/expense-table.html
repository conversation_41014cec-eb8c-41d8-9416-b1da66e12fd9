{% load static %}
{% load humanize %}
{% load i18n %}
{% load hosts %}
{% load custom_tags %}
{% get_current_language as LANGUAGE_CODE %}


<div class="d-flex d-flex align-items-center mb-2" style="background-color: #FAFAFA !important;">
    {% include 'data/common/module-header-tabs.html' %}

    <div class="w-50 d-flex justify-content-end tw-mr-5" id="view-container">
        {% if object_type == constant.TYPE_OBJECT_EXPENSE %}
        <div class="{% include "data/utility/table-button.html" %}">
            <button id='view-sync-items-action-drawer' type="button" class="max-md:tw-hidden w-125px align-items-center d-flex svg-icon-gray-600 btn py-1 rounded-1 tw-bg-gray-200 hover:tw-bg-gray-300 justify-content-center px-1 create-view-settings-button"
                hx-get="{% url 'expense_action' %}" 
                hx-trigger="click"
                onclick="fillActionExpenseIds(this),check_permission_action(event, 'edit')"
                hx-target="#manage-contacts-view-settings-drawer"
                hx-indicator=".loading-drawer-spinner,.view-form"
                >
                <span class="svg-icon svg-icon-4">
                    <svg width="14" height="16" viewBox="0 0 14 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M8.49968 1.33301H4.66264C4.54298 1.33301 4.48316 1.33301 4.43034 1.35123C4.38363 1.36734 4.34109 1.39363 4.30579 1.4282C4.26587 1.4673 4.23912 1.52081 4.18561 1.62783L1.38561 7.22783C1.25782 7.4834 1.19393 7.61119 1.20927 7.71506C1.22268 7.80576 1.27285 7.88694 1.34798 7.93949C1.43403 7.99967 1.5769 7.99967 1.86264 7.99967H5.99968L3.99968 14.6663L12.1284 6.23655C12.4027 5.95214 12.5398 5.80994 12.5478 5.68826C12.5548 5.58265 12.5112 5.48 12.4303 5.4117C12.3371 5.33301 12.1396 5.33301 11.7445 5.33301H6.99968L8.49968 1.33301Z" stroke="#3B3B3F" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                    </svg>
                </span>
                <span class="">
                    {% if LANGUAGE_CODE == 'ja'%}
                    アクション
                    {% else %}
                    Action
                    {% endif %}
                </span>
            </button>
        </div>

        <script>
            {% if open_drawer == 'action_drawer_history' %}
                $(document).ready(function() {
                    setTimeout(function() {
                        document.getElementById('view-sync-items-action-drawer').click()
                    }, 1000)
                })
            {% endif %}
            function fillActionExpenseIds(elm) {
                // Call your JavaScript function to generate the account IDs dynamically
                var checkboxes = document.querySelectorAll('input[type="checkbox"]');
                var checkedIds = [];
                checkboxes.forEach(function(checkbox) {
                    if (checkbox.checked) {
                        checkedIds.push(checkbox.value);
                    }
                });
                var orderIds = checkedIds;
                orderIds = orderIds.filter(id => id !== 'on');
                // Now set the hx-vals attribute with the updated account IDs
                elm.setAttribute('hx-vals', 'js:{"section": "action_history", "page": "{{page}}","action_tab":"action", "expense_ids":getSelectedExpense(), "open_drawer": "{{open_drawer}}"}');
            }
        </script>
        {% endif %}

        <div class="{% include "data/utility/header-action-button.html" %}">
            <button id="payment_wizard_button" type="button" class="btn tw-font-[500] tw-rounded-l-lg tw-rounded-r-none tw-border-0 tw-bg-gray-200 hover:tw-bg-gray-300 max-md:tw-hidden w-125px align-items-center d-flex svg-icon-gray-600 py-1 justify-content-center px-1"
                hx-vals='{"object_type": "{{object_type}}", "view_id":"{{current_view.id}}", "download_view":true, "import_export_type":"export"}'
                hx-get="{% url 'commerce_view_setting' %}" 
                hx-target="#expenses_form"
                hx-on="htmx:beforeSend: 
                    document.getElementById('expenses_form_lg').innerHTML = '';
                    "
                >

                <span class="svg-icon svg-icon-4">
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-upload" viewBox="0 0 16 16">
                        <path d="M.5 9.9a.5.5 0 0 1 .5.5v2.5a1 1 0 0 0 1 1h12a1 1 0 0 0 1-1v-2.5a.5.5 0 0 1 1 0v2.5a2 2 0 0 1-2 2H2a2 2 0 0 1-2-2v-2.5a.5.5 0 0 1 .5-.5"/>
                        <path d="M7.646 1.146a.5.5 0 0 1 .708 0l3 3a.5.5 0 0 1-.708.708L8.5 2.707V11.5a.5.5 0 0 1-1 0V2.707L5.354 4.854a.5.5 0 1 1-.708-.708z"/>
                    </svg>
                </span>
                <span class="">
                    {% if LANGUAGE_CODE == 'ja'%}
                    エクスポート
                    {% else %}
                    Export
                    {% endif %}
                </span>

            </button>

            {% if object_type == constant.TYPE_OBJECT_PURCHASE_ORDER or object_type == constant.TYPE_OBJECT_EXPENSE or object_type == constant.TYPE_OBJECT_BILL %}
            <button type="button" class="payment_wizard_button btn tw-font-[500] tw-rounded-l-none tw-rounded-r-lg tw-border-0 tw-bg-gray-200 hover:tw-bg-gray-300 max-md:tw-hidden w-125px align-items-center d-flex svg-icon-gray-600 py-1 justify-content-center px-1"
                hx-vals='{"object_type": "{{object_type}}", "view_id":"{{current_view.id}}", "download_view":true, "import_export_type":"import"}'
                hx-get="{% url 'commerce_view_setting' %}" 
                hx-target="#expenses_form"
                hx-on="htmx:beforeSend: 
                    document.getElementById('expenses_form_lg').innerHTML = '';
                    "
                >

                <span class="svg-icon svg-icon-4">
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-download" viewBox="0 0 16 16">
                        <path d="M.5 9.9a.5.5 0 0 1 .5.5v2.5a1 1 0 0 0 1 1h12a1 1 0 0 0 1-1v-2.5a.5.5 0 0 1 1 0v2.5a2 2 0 0 1-2 2H2a2 2 0 0 1-2-2v-2.5a.5.5 0 0 1 .5-.5"/>
                        <path d="M7.646 11.854a.5.5 0 0 0 .708 0l3-3a.5.5 0 0 0-.708-.708L8.5 10.293V1.5a.5.5 0 0 0-1 0v8.793L5.354 8.146a.5.5 0 1 0-.708.708z"/>
                    </svg>
                </span>
                <span class="">
                    {% if LANGUAGE_CODE == 'ja'%}
                    インポート
                    {% else %}
                    Import
                    {% endif %}
                </span>

            </button>
            {% endif %}
        </div>
        {% if permission|check_permission:'edit' %}
        <div class="btn-group tw-h-[32px]" data-bs-toggle="tooltip" data-bs-placement="right" data-bs-delay-show="1000" data-kt-initialized="1">
            <button class="w-100px align-items-center d-flex btn btn-primary btn-md py-1 rounded-1 view_form_trigger"
                {% if object_type == "billing"  %}
                    style="border-radius: 0.475rem 0 0 0.475rem !important;"
                    hx-vals='{"object_type": "billing", "drawer_type":"billing", "module": "{{menu_key}}","set_id":"{{set_id}}"}'
                    hx-get="{% url 'bill_drawer' %}" 
                    hx-indicator=".loading-drawer-spinner,.expenses-form"
                    hx-target="#expenses_form"
                    hx-on::before-request="document.getElementById('expenses_form').innerHTML = ''"
                    id="payment_wizard_button"  
                {% elif object_type == constant.TYPE_OBJECT_EXPENSE  %}
                    style="border-radius: 0.475rem 0 0 0.475rem !important;"
                    hx-vals='{"drawer_type":"expenses", "module": "{{menu_key}}","set_id":"{{set_id}}"}'
                    hx-get="{% url 'expense_drawer_new' %}" 
                    hx-indicator=".loading-drawer-spinner,.expenses-form"
                    hx-target="#expenses_form"
                    hx-on::before-request="document.getElementById('expenses_form').innerHTML = ''"
                    id="payment_wizard_button"  
                {% elif object_type == "purchaseorder" %}
                    style="border-radius: 0.475rem 0 0 0.475rem !important;"
                    hx-vals='{"drawer_type":"purchaseorder", "view_id":"{{current_view.id}}", "module" : "{{menu_key}}", "set_id":"{{set_id}}"}'
                    hx-get="{% url 'purchase_drawer' %}" 
                    hx-indicator=".loading-drawer-spinner,.procurement-form"
                    hx-target="#procurement_form" 
                    hx-on::before-request="document.getElementById('procurement_form').innerHTML = ''"
                    hx-swap="innerHTML"
                    id="procurement_wizard_button"  
                {% elif object_type == "purchaseitem" %}
                    hx-vals='{"drawer_type":"purchaseitem"}'
                    hx-get="{% url 'purchaseItemDrawer' %}" 
                    hx-indicator=".loading-drawer-spinner,.expenses-form"
                    hx-target="#expenses_form"
                    hx-on::before-request="document.getElementById('expenses_form').innerHTML = ''"
                    id="payment_wizard_button"  
                {% endif %}
                type="button">
                <span class="svg-icon svg-icon-4">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <rect opacity="0.3" x="2" y="2" width="20" height="20" rx="10" fill="currentColor"/>
                        <rect x="10.8891" y="17.8033" width="12" height="2" rx="1" transform="rotate(-90 10.8891 17.8033)" fill="currentColor"/>
                        <rect x="6.01041" y="10.9247" width="12" height="2" rx="1" fill="currentColor"/>
                    </svg>
                </span>
                <span class="fs-7 ps-1 fw-bolder w-85px">
                    {% if LANGUAGE_CODE == 'ja'%}
                    新規
                    {% else %}
                    New
                    {% endif %}
                </span>
            </button>
            {% if object_type == "purchaseorder" %}
                <button type="button" 
                    class="tw-w-[30px] align-items-center d-flex btn btn-primary btn-md dropdown-toggle py-1 tw-pl-2 dropdown-toggle-split" 
                    data-bs-toggle="dropdown" 
                    aria-expanded="false"
                    style="border-radius: 0 0.475rem 0.475rem 0;border-left: 1px solid #dee2e6;"
                >
                    <span class="svg-icon svg-icon-4">
                        <svg width=24 height=24 xmlns="http://www.w3.org/2000/svg" viewBox="0 0 320 512" fill="currentColor">
                            <path d="M137.4 374.6c12.5 12.5 32.8 12.5 45.3 0l128-128c9.2-9.2 11.9-22.9 6.9-34.9s-16.6-19.8-29.6-19.8L32 192c-12.9 0-24.6 7.8-29.6 19.8s-2.2 25.7 6.9 34.9l128 128z"/>
                        </svg>
                    </span>
                </button>
                <ul class="dropdown-menu tw-max-w-[180px] tw-max-h-[300px] tw-overflow-y-scroll">
                    {% for set in property_sets %}
                    <li>
      
                        <button class="dropdown-item tw-text-ellipsis tw-overflow-hidden class-procurement_wizard_button" type="button"

                            hx-vals='{"drawer_type":"purchaseorder", "view_id":"{{view_id}}", "set_id": "{{set.id}}"}'
                            hx-get="{% url 'purchase_drawer' %}" 
                            
                            hx-target=".target-procurement_form" 
                            hx-swap="innerHTML"
                            hx-trigger="click-manual"
                            onclick="trigger(this)"
                            style="border-radius: 0.475rem 0 0 0.475rem;"
                            >
                            {% if set.name %}
                                {{ set.name}}
                            {% else %}
                                {% if LANGUAGE_CODE == 'ja' %}デフォルト{% else %}Default{% endif %} 
                            {% endif %}
                        </button>
                    </li>
                    {% endfor %}
                </ul>  
            {% elif object_type == "billing" %}
                <button type="button" 
                    class="tw-w-[30px] align-items-center d-flex btn btn-primary btn-md dropdown-toggle py-1 tw-pl-2 dropdown-toggle-split" 
                    data-bs-toggle="dropdown" 
                    aria-expanded="false"
                    style="border-radius: 0 0.475rem 0.475rem 0;border-left: 1px solid #dee2e6;"
                >
                    <span class="svg-icon svg-icon-4">
                        <svg width=24 height=24 xmlns="http://www.w3.org/2000/svg" viewBox="0 0 320 512" fill="currentColor">
                            <path d="M137.4 374.6c12.5 12.5 32.8 12.5 45.3 0l128-128c9.2-9.2 11.9-22.9 6.9-34.9s-16.6-19.8-29.6-19.8L32 192c-12.9 0-24.6 7.8-29.6 19.8s-2.2 25.7 6.9 34.9l128 128z"/>
                        </svg>
                    </span>
                </button>
                <ul class="dropdown-menu tw-max-w-[180px] tw-max-h-[300px] tw-overflow-y-scroll">
                    <li>
      
                        <button class="dropdown-item tw-text-ellipsis tw-overflow-hidden class-procurement_wizard_button" type="button"

                            {% comment %} hx-vals='{"drawer_type":"billing"}' {% endcomment %}
                            hx-vals='{"object_type":"billing"}'
                            hx-get="{% url 'purchase_order_bot' %}" 
                            
                            hx-target=".target-procurement_form" 
                            hx-swap="innerHTML"
                            hx-trigger="click-manual"
                            onclick="trigger(this)"
                            style="border-radius: 0.475rem 0 0 0.475rem;"
                            >
                            {% if set.name %}
                                {{ set.name}}
                            {% else %}
                                {% if LANGUAGE_CODE == 'ja' %}一括アップロード{% else %}Bulk Upload{% endif %} 
                            {% endif %}
                        </button>
                    </li>
                </ul>
            {% elif object_type == constant.TYPE_OBJECT_EXPENSE %}
                <button type="button" 
                    class="tw-w-[30px] align-items-center d-flex btn btn-primary btn-md dropdown-toggle py-1 tw-pl-2 dropdown-toggle-split" 
                    data-bs-toggle="dropdown" 
                    aria-expanded="false"
                    style="border-radius: 0 0.475rem 0.475rem 0;border-left: 1px solid #dee2e6;"
                >
                    <span class="svg-icon svg-icon-4">
                        <svg width=24 height=24 xmlns="http://www.w3.org/2000/svg" viewBox="0 0 320 512" fill="currentColor">
                            <path d="M137.4 374.6c12.5 12.5 32.8 12.5 45.3 0l128-128c9.2-9.2 11.9-22.9 6.9-34.9s-16.6-19.8-29.6-19.8L32 192c-12.9 0-24.6 7.8-29.6 19.8s-2.2 25.7 6.9 34.9l128 128z"/>
                        </svg>
                    </span>
                </button>
                <ul class="dropdown-menu tw-max-w-[180px] tw-max-h-[300px] tw-overflow-y-scroll">
                    <li>
        
                        <button class="dropdown-item tw-text-ellipsis tw-overflow-hidden class-procurement_wizard_button" type="button"

                            {% comment %} hx-vals='{"drawer_type":"billing"}' {% endcomment %}
                            hx-vals='{"object_type":"expense"}'
                            hx-get="{% url 'purchase_order_bot' %}" 
                            
                            hx-target=".target-procurement_form" 
                            hx-swap="innerHTML"
                            hx-trigger="click-manual"
                            onclick="trigger(this)"
                            style="border-radius: 0.475rem 0 0 0.475rem;"
                            >
                            {% if set.name %}
                                {{ set.name}}
                            {% else %}
                                {% if LANGUAGE_CODE == 'ja' %}一括アップロード{% else %}Bulk Upload{% endif %} 
                            {% endif %}
                        </button>
                    </li>
                </ul>
            {% endif %}
            <script>
                {% comment %} selectContent = document.getElementById('item-checker') {% endcomment %}
                //htmx.trigger(selectContent, 'click-manual');
                function trigger(elm){
                    console.log("elm: ", elm)
                    htmx.trigger(elm, 'click-manual');
                }
            </script>
        </div>
        {% endif %}
        <div class="{% include "data/utility/table-button.html" %}">
            <button class="payment_wizard_button max-md:tw-block tw-hidden tw-w-[28px] tw-h-[26px] tw-justify-center tw-items-center tw-border-0 tw-bg-transparent tw-mb-2 tw-cursor-pointer hover-tooltip"
            type="button"
            hx-vals='{"object_type": "{{object_type}}", "view_id":"{{current_view.id}}", "download_view":true}'
            hx-get="{% url 'commerce_view_setting' %}" 
            hx-target="#expenses_form"
            hx-on="htmx:beforeSend: 
                document.getElementById('expenses_form_lg').innerHTML = '';
                ">
                <span class="search-wrapper-tooltip hover-tooltip-text">
                    {% if LANGUAGE_CODE == 'ja' %}ダウンロード{% else %}Download{% endif %}
                </span>
                <span class="tw-flex svg-icon svg-icon-3">
                    <svg xmlns="http://www.w3.-listorg/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                        <path d="M21 22H3C2.4 22 2 21.6 2 21C2 20.4 2.4 20 3 20H21C21.6 20 22 20.4 22 21C22 21.6 21.6 22 21 22ZM13 13.4V3C13 2.4 12.6 2 12 2C11.4 2 11 2.4 11 3V13.4H13Z" fill="black"/>
                        <path opacity="0.3" d="M7 13.4H17L12.7 17.7C12.3 18.1 11.7 18.1 11.3 17.7L7 13.4Z" fill="black"/>
                    </svg>
                </span>
            </button>
            
        </div>

    </div>
</div>
{% if permission == 'hide' %}
    {% include 'data/static/no-access-page.html' %}
{% else %}
<div class="{% include "data/utility/table-container.html" %}">
{% include 'data/common/permission-action-warning-message.html' %}
{% include 'data/expense/expense-view-menu.html' %}

    <form id="bulk-update" method="POST" action="{% host_url 'load_object_page' menu_key object_type|object_type_slug:request host 'app' %}">
        {% csrf_token %}
        <input hidden name='query' value='{% get_page_query %}'>
        <input hidden name='module' value='{{menu_key}}'>
        <input hidden name="view_id" value="{{current_view.id}}" />
        {% if object_type == constant.TYPE_OBJECT_PURCHASE_ORDER %}
            <input hidden name='flag_all' id="flag_all" type="checkbox" />
        {% endif %}
    
        {% include 'data/expense/expense-table-section.html'%}
    </form>
    {% endif %}

    {% include "data/common/paginator/paginator.html" with redirect_url=pagination_url %}
</div>

<script>
    function check_permission_action(event, permission_type, ...args){
        let source = args.length > 0 ? args[0] : null;
        
        const checkInputs = document.querySelectorAll('.check_input:checked');

        let members = "{{group_members}}"
        members = members.split(',')
        const user_id = '{{request.user.id}}'
        const permission = '{{permission}}';
        const permission_list = permission.split('|');
        let scope = ''
        permission_list.forEach(p => {
            if (p.includes(permission_type)) {
                p_split = p.split('_');
                scope = p_split[0]
            }
        })

        let msg = '';
        let denied = false;
        for (let i = 0; i < checkInputs.length; i++) {
            var owner_id = checkInputs[i].dataset.owner;
            if (owner_id){
                if (scope == 'user'){
                    if (owner_id.toString() !== user_id.toString()) {
                        {% if LANGUAGE_CODE == 'ja' %}
                        msg = "操作が拒否されました。自分のアイテムのみ編集または削除することができます。";
                        {% else %}
                        msg = "Action denied. You are only allowed to edit or delete your own items.";
                        {% endif %}                    
                        checkInputs[i].click()
                        denied = true;
                        console.log(user_id)
                        console.log(owner_id)
                    }
                } else if (scope == 'team'){
                    if (!members.includes(owner_id.toString())) {
                        {% if LANGUAGE_CODE == 'ja' %}
                        msg = "操作が拒否されました。自分または自分のチームに割り当てられたアイテムのみ編集または削除できます。";
                        {% else %}
                        msg = "Action denied. You can only edit or delete items assigned to you or your team.";
                        {% endif %}
                        checkInputs[i].click()
                        denied = true;
                    }
                } 
            }
        }
        if (denied) {
            event.preventDefault();
            document.getElementById('permissionActionWarning').innerHTML = msg;
            setTimeout(() => {
                document.getElementById('permissionActionWarning').innerHTML = '';
            }, 4000);
            msg = ''
        } else if (source === 'edit_modal'){
            const modalEl = document.getElementById('edit_bulk_modal');
            const modal = bootstrap.Modal.getOrCreateInstance(modalEl);
            modal.show();
        }
        
    }
</script>
