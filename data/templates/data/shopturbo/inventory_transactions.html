{% extends 'base.html' %}
{% load i18n %}
{% load custom_tags %}
{% load humanize %}
{% load hosts %}
{% block content %}

{% include "data/common/advance_search/advance-search-style.html" %}

<style>
    table {
        width: auto;
    }
    
    td, th {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    thead.position-sticky {
        position: sticky;
        top: 0;
        background: white; 
        z-index: 10; 
    }
</style>

<style>
    .search-wrapper {
        display: flex;
        align-items: center;
        position: relative;
        width: 24px;
        transition: width 0.4s ease-in-out, margin-right 0.4s ease-in-out, opacity 0.4s ease-in-out;
    }

    .search-wrapper.expanded {
        width: 200px; /* New width when expanded */
        margin-right: -0.5rem !important;
    }

    .search-wrapper input {
        display: none;
        width: 0;
        padding: 0;
        opacity: 0;
        transition: width 0.4s ease-in-out, margin-right 0.4s ease-in-out, opacity 0.4s ease-in-out;
    }

    .search-wrapper.expanded input {
        display: block;
        width: 100%;
        opacity: 1;
    }
    

    .search-icon-view {
        position: absolute;
        left: 10px;
        top: 50%;
        transform: translateY(-50%);
    }

    /* Tooltip container */
    .hover-tooltip {
        position: relative;
        display: inline-block;
    }

    /* Tooltip text */
    .hover-tooltip .hover-tooltip-text {
        visibility: hidden;
        width: 80px;
        background-color: #555;
        color: #fff;
        text-align: center;
        padding: 3px 0;
        border-radius: 8px;

        /* Position the hover-tooltip text */
        position: absolute;
        z-index: 1 !important;
        top: 50%;
        right: 105%;
        transform: translateY(-50%);

        /* Fade in hover-tooltip */
        opacity: 0;
        transition: opacity 0.5s;
    }

    /* Show the hover-tooltip text when you mouse over the hover-tooltip container */
    .hover-tooltip:hover .hover-tooltip-text {
        visibility: visible;
        opacity: 0.9;
    }

    .dataTables_scrollHead {
        position: sticky !important;
        z-index: 3;
        background-color: white;
    }
</style>

<div class="d-flex d-flex align-items-center mb-2" style="background-color: #FAFAFA !important;">
    {% include 'data/common/module-header-tabs.html' %}

    <div class="w-50 d-flex justify-content-end tw-mr-5" id="transaction-view-container">
        <div class="{% include "data/utility/header-action-button.html" %}">
            <button class="btn tw-font-[500] tw-rounded-l-lg tw-rounded-r-none tw-border-0 tw-bg-gray-200 hover:tw-bg-gray-300 max-md:tw-hidden w-125px align-items-center d-flex svg-icon-gray-600 py-1 justify-content-center px-1 create-view-settings-button" type="button"
                hx-get="{% url 'shopturbo_load_drawer' %}" 
                hx-vals='{"module":"{{menu_key}}"}'
                hx-trigger="click"
                onclick="fillItemExportIds(this)"
                hx-target="#manage-contacts-view-settings-drawer"

                >
                <span class="svg-icon svg-icon-4">
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-upload" viewBox="0 0 16 16">
                        <path d="M.5 9.9a.5.5 0 0 1 .5.5v2.5a1 1 0 0 0 1 1h12a1 1 0 0 0 1-1v-2.5a.5.5 0 0 1 1 0v2.5a2 2 0 0 1-2 2H2a2 2 0 0 1-2-2v-2.5a.5.5 0 0 1 .5-.5"/>
                        <path d="M7.646 1.146a.5.5 0 0 1 .708 0l3 3a.5.5 0 0 1-.708.708L8.5 2.707V11.5a.5.5 0 0 1-1 0V2.707L5.354 4.854a.5.5 0 1 1-.708-.708z"/>
                    </svg>
                </span>
                <span class="">
                    {% if LANGUAGE_CODE == 'ja'%}
                    エクスポート
                    {% else %}
                    Export
                    {% endif %}
                </span>

            </button> 
            <script>
                function fillItemExportIds(elm) {
                    // Call your JavaScript function to generate the account IDs dynamically
                    var checkboxes = document.querySelectorAll('input[type="checkbox"]');
                    var checkedIds = [];
                    checkboxes.forEach(function(checkbox) {
                        if (checkbox.checked) {
                            checkedIds.push(checkbox.value);
                        }
                    });
                    var itemIds = checkedIds;
                    itemIds = itemIds.filter(id => id !== 'on');
                    // Now set the hx-vals attribute with the updated account IDs
                    elm.setAttribute('hx-vals', '{"drawer_type":"view-sync-inventory-transaction","page": "inventory-transaction","import_export_type":"export","view_id":"{{view_filter.view.id}}", "inventory_transactions_ids":"' + itemIds + '", "module":"{{menu_key}}"}');
                }
            </script>

            {% if permission|check_permission:'edit' %}
                <button id='view-sync-items' type="button" class="{% include "data/utility/import-button.html" %}"
                    style="width: 130px"
                    hx-get="{% url 'shopturbo_load_drawer' %}" 
                    hx-vals='{"module":"{{menu_key}}"}'
                    hx-trigger="click"
                    onclick="fillOrderIds(this)"
                    hx-target="#manage-contacts-view-settings-drawer"
                    >
                    <span class="svg-icon svg-icon-4">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-download" viewBox="0 0 16 16">
                            <path d="M.5 9.9a.5.5 0 0 1 .5.5v2.5a1 1 0 0 0 1 1h12a1 1 0 0 0 1-1v-2.5a.5.5 0 0 1 1 0v2.5a2 2 0 0 1-2 2H2a2 2 0 0 1-2-2v-2.5a.5.5 0 0 1 .5-.5"/>
                            <path d="M7.646 11.854a.5.5 0 0 0 .708 0l3-3a.5.5 0 0 0-.708-.708L8.5 10.293V1.5a.5.5 0 0 0-1 0v8.793L5.354 8.146a.5.5 0 1 0-.708.708z"/>
                        </svg>
                    </span>
                    <span class="">
                        {% if LANGUAGE_CODE == 'ja'%}
                        インポート
                        {% else %}
                        Import
                        {% endif %}
                    </span>

                </button>
                
                <script>
                    function fillOrderIds(elm) {
                        // Call your JavaScript function to generate the account IDs dynamically
                        var checkboxes = document.querySelectorAll('input[type="checkbox"]');
                        var checkedIds = [];
                        checkboxes.forEach(function(checkbox) {
                            if (checkbox.checked) {
                                checkedIds.push(checkbox.value);
                            }
                        });
                        var orderIds = checkedIds;
                        orderIds = orderIds.filter(id => id !== 'on');
                        // Now set the hx-vals attribute with the updated account IDs
                        elm.setAttribute('hx-vals', '{"drawer_type":"view-sync-inventory-transaction","import_export_type":"import","inventory_transactions_ids":"' + orderIds + '", "module":"{{menu_key}}"}');
                    }
                </script>
            {% endif %}
        </div>

        {% if permission|check_permission:'edit' %}
            <div class="btn-group tw-h-[32px]">
                <button class="tw-w-[100px] align-items-center d-flex btn btn-primary btn-md view_form_trigger shopturbo-create-wizard-button py-1" type="button"
                    hx-get="{% url 'shopturbo_load_drawer' %}" 
                    hx-vals = '{"drawer_type":"inventory-transaction-create", "type": "transactions", "module":"{{menu_key}}"}'
                    hx-target="#shopturbo-create-drawer-content"
                    hx-indicator=".loading-drawer-spinner,#shopturbo-create-drawer-content" 
                    style="border-radius: 0.475rem 0 0 0.475rem;"
                    hx-trigger="click,manual-click"
                    hx-on::before-request="document.getElementById('shopturbo-create-drawer-content').innerHTML = ''"
                    >
                    <span class="svg-icon svg-icon-4">
                        <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M8.00065 3.33301V12.6663M3.33398 7.99967H12.6673" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                    </span>
            
                    <span class="fs-7 ps-1 fw-bolder w-85px">
                        {% if LANGUAGE_CODE == 'ja'%}
                        新規
                        {% else %}
                        New
                        {% endif %}
                    </span>
                </button>
                <button type="button" 
                    class="tw-w-[30px] align-items-center d-flex btn btn-primary btn-md dropdown-toggle py-1 tw-pl-2 dropdown-toggle-split" 
                    data-bs-toggle="dropdown" 
                    aria-expanded="false"
                    style="border-radius: 0 0.475rem 0.475rem 0;border-left: 1px solid #dee2e6;"
                >
                    <span class="svg-icon svg-icon-4">
                        <svg width=24 height=24 xmlns="http://www.w3.org/2000/svg" viewBox="0 0 320 512" fill="currentColor">
                            <path d="M137.4 374.6c12.5 12.5 32.8 12.5 45.3 0l128-128c9.2-9.2 11.9-22.9 6.9-34.9s-16.6-19.8-29.6-19.8L32 192c-12.9 0-24.6 7.8-29.6 19.8s-2.2 25.7 6.9 34.9l128 128z"/>
                        </svg>
                    </span>
                </button>
                <ul class="dropdown-menu tw-max-w-[220px] tw-max-h-[300px] tw-overflow-y-scroll">
                    <li>
                        <button class="dropdown-item tw-text-ellipsis tw-overflow-hidden shopturbo-manage-wizard-button" type="button"
                            hx-get="{% url 'inventory_transaction_barcode_scan' %}" 
                            hx-vals = '{"view_id": "{{view_id}}", "module": "{{menu_key}}"}'
                            hx-target="#shopturbo-drawer-content"
                            hx-indicator=".loading-drawer-spinner,#shopturbo-drawer-content"
                            style="border-radius: 0.475rem 0 0 0.475rem;"
                            >
                        {% if LANGUAGE_CODE == 'ja'%}バーコード入力{% else %}Barcode Entry{% endif %}
                        </button>
                    </li>
                    <li>
                        <button class="dropdown-item tw-text-ellipsis tw-overflow-hidden shopturbo-manage-wizard-button" type="button"
                            hx-get="{% url 'shopturbo_load_drawer' %}" 
                            hx-vals = '{"drawer_type":"barcode", "view_id": "{{view_id}}", "section": "inventory-transaction-entry-by-order", "module": "{{menu_key}}"}'
                            hx-target="#shopturbo-drawer-content"
                            hx-indicator=".loading-drawer-spinner,#shopturbo-drawer-content"
                            style="border-radius: 0.475rem 0 0 0.475rem;"
                            >
                        {% if LANGUAGE_CODE == 'ja'%}受注商品バーコード入力{% else %}Order Item Barcode Entry{% endif %}
                        </button>
                    </li>
                </ul>   
            </div>
        {% endif %}
    </div>
</div>

{% if permission == 'hide' %}
    {% include 'data/static/no-access-page.html' %}
{% else %}
<div class="w-100 tw-pl-2">
    {% include 'data/common/permission-action-warning-message.html' %}
    {% comment %} Views Part {% endcomment %}
    <div class="{% include "data/utility/tab-pane.html" %}" role="tabpanel" style="z-index:4 !important;">
        <div class="{% include "data/utility/table-nav.html" %}">
            <div class="d-flex align-items-end justify-content-between w-100" id="transaction-view-container-1">
                <div class="w-50 d-flex align-items-center">
                    <div class="nav-item fs-6 text-gray-900">
                        <div class="d-flex align-items-center justify-content-center">
                            <button type="button" class="align-items-center justify-content-center d-flex btn text-gray-600 bg-transparent text-hover-primary tw-px-[5px] mb-2 tw-w-[30px] create-view-settings-button" 
                                hx-vals='{"drawer_type":"shopturbo-view-settings","page": "inventory-transaction", "module":"{{menu_key}}"}'
                                hx-get="{% url 'shopturbo_load_drawer' %}" 
                                hx-target="#manage-contacts-view-settings-drawer"
                                hx-swap="innerHTML"
                                style="height: 26px;"
                            >
                                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="currentColor" class="bi bi-plus" viewBox="0 0 16 16">
                                    <path d="M8 4a.5.5 0 0 1 .5.5v3h3a.5.5 0 0 1 0 1h-3v3a.5.5 0 0 1-1 0v-3h-3a.5.5 0 0 1 0-1h3v-3A.5.5 0 0 1 8 4z"/>
                                </svg>
                            </button>
                        </div>
                    </div>
                    
                    {% include "data/projects/partial-dropdown-view-menu.html" %}
                    <div class="{% include "data/utility/view-menu-nav-item.html" %}">
                        <a class="{% include "data/utility/view-menu-default.html" %}" 
                            type="button"
                            href = "{% host_url 'load_object_page' menu_key object_type|object_type_slug:request host 'app' %}">
                            <span class="svg-icon svg-icon-muted svg-icon-2"><svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M22 12C22 17.5 17.5 22 12 22C6.5 22 2 17.5 2 12C2 6.5 6.5 2 12 2C17.5 2 22 6.5 22 12ZM12 6C8.7 6 6 8.7 6 12C6 15.3 8.7 18 12 18C15.3 18 18 15.3 18 12C18 8.7 15.3 6 12 6Z" fill="currentColor"/>
                                </svg>
                            </span>
                        </a>
                        {% if not view_filter.view.title %}
                        <div class="text-gray-900 w-20px nav-item justify-content-center d-flex fs-6">
                            <button type="button" class="{% include "data/utility/view-plus-link.html" %} create-view-settings-button"
                                hx-vals='{"drawer_type":"shopturbo-view-settings","page": "inventory-transaction", "view_id":"{{view_filter.view.id}}", "module":"{{menu_key}}"}'
                                hx-get="{% url 'shopturbo_load_drawer' %}" 
                                hx-indicator=".loading-drawer-spinner,.view-form"
                                hx-target="#manage-contacts-view-settings-drawer"
                                hx-trigger="click"
                                hx-swap="innerHTML"
                                >
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-three-dots-vertical" viewBox="0 0 16 16">
                                    <path d="M9.5 13a1.5 1.5 0 1 1-3 0 1.5 1.5 0 0 1 3 0zm0-5a1.5 1.5 0 1 1-3 0 1.5 1.5 0 0 1 3 0zm0-5a1.5 1.5 0 1 1-3 0 1.5 1.5 0 0 1 3 0z"/>
                                </svg>
                            </button>
                        </div>
                        {% endif %}
                    </div>

                    {% comment %} Put Items here {% endcomment %}
                    {% include 'data/projects/partial-view-menu.html' %}

                </div>
                <div class="d-flex w-50 justify-content-end pe-5">
                    <div class="me-2">
                        <div class="mb-2 search-wrapper expanded">
                            <div class="d-flex align-items-center">
                                <form id="filter-form-search" method="get" class="w-100">
                                    <div class="mb-0 d-flex position-relative" style="height: 26px;">
                                        <span class="svg-icon svg-icon-3 search-icon-view">
                                            <svg width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <path d="M13 13L10.1 10.1M11.6667 6.33333C11.6667 9.27885 9.27885 11.6667 6.33333 11.6667C3.38781 11.6667 1 9.27885 1 6.33333C1 3.38781 3.38781 1 6.33333 1C9.27885 1 11.6667 3.38781 11.6667 6.33333Z" stroke="#8E8C95" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                                            </svg>
                                        </span>
                                        <input
                                        id="base-search-input" type="text" name="q" class="bg-white form-control ps-12 pe-16 tw-rounded-lg" 
                                        value="{% if advance_search_filter %}{% for k, v in advance_search_filter.items %}{% if k|search_custom_field_object_inventory_transaction:request %}{% with channel_column=k|search_custom_field_object_inventory_transaction:request %}{{channel_column.name}}{% endwith %}{% else %}{% with column_display=k|display_column_inventory_transaction:request %}{{column_display}}{% endwith %}{% endif %} {% translate_lang v.key|display_filter_rule:LANGUAGE_CODE LANGUAGE_CODE %} {{ v.value }}{% if not forloop.last %}, {% endif %}{% endfor %}{% elif search_q %}{{ search_q }}{% else %}{% endif %}"
                                        placeholder={% if LANGUAGE_CODE == 'ja' %} "検索" {% else %} "Search" {% endif %}
                                        onkeypress="if (event.keyCode == 13)document.forms['filter-form-search'].submit();"
                                        >
                                        {% if request.GET.status == 'archived' %}
                                        <input type="hidden" value="archived" name="status">
                                        {% endif %}
                                        <span class="svg-icon {% if advance_search.is_active %}svg-icon-primary{%else%}svg-icon-muted{% endif %} svg-icon-3 filter-icon filter-drawer-btn me-7"
                                            hx-get="{% url 'advance_search_drawer' %}"
                                            hx-vals='{"object_type": "{{object_type}}", "module": "{{menu_key}}", "view_id": "{{view_id}}"}'
                                            hx-indicator=".loading-drawer-spinner"
                                            hx-target="#filter-drawer-content"
                                            hx-trigger="click"
                                            hx-swap="innerHTML"
                                        >
                                            <svg width="14" height="10" viewBox="0 0 14 10" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <path d="M3 5H11M1 1H13M5 9H9" stroke="#8E8C95" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                                            </svg>
                                        </span>
                                        <span class="svg-icon svg-icon-muted svg-icon-3 filter-icon filter-drawer-btn"
                                            hx-get="{% url 'advance_search_drawer' %}"
                                            hx-vals='{"object_type": "{{object_type}}", "module": "{{menu_key}}", "search_scope":"True", "view_id": "{{view_id}}"}'
                                            hx-indicator=".loading-drawer-spinner"
                                            hx-target="#filter-drawer-content"
                                            hx-trigger="click"
                                            hx-swap="innerHTML"
                                        >
                                            <svg width="14" height="12" viewBox="0 0 14 12" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <path d="M1 3.33301L9 3.33301M9 3.33301C9 4.43758 9.89543 5.33301 11 5.33301C12.1046 5.33301 13 4.43758 13 3.33301C13 2.22844 12.1046 1.33301 11 1.33301C9.89543 1.33301 9 2.22844 9 3.33301ZM5 8.66634L13 8.66634M5 8.66634C5 9.77091 4.10457 10.6663 3 10.6663C1.89543 10.6663 1 9.77091 1 8.66634C1 7.56177 1.89543 6.66634 3 6.66634C4.10457 6.66634 5 7.56177 5 8.66634Z" stroke="#8E8C95" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                                            </svg>
                                        </span>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div id="transaction-bulk-action-container" class="d-none d-flex w-100">
                <div class="max-md:tw-block tw-flex w-50">
                    <div class="me-10 d-flex" style="min-width: 30px">
                        <button class="py-1 mt-2 mb-1 rounded-1 btn btn-sm btn-light fw-bold me-2" onclick="selectAllContacts()">
                            <span>
                                {% if LANGUAGE_CODE == 'ja'%}
                                すべて選択
                                {% else %}
                                Select All
                                {% endif %}
                            </span>
                        </button>
                        <button class="py-1 mt-2 mb-1 rounded-1 btn btn-sm btn-light fw-bold me-2" onclick="deselectAllContacts()">
                            <span>
                                {% if LANGUAGE_CODE == 'ja'%}
                                選択を解除
                                {% else %}
                                Deselect All
                                {% endif %}
                            </span>
                        </button>
                    
        
                        <script>
                            function selectAllContacts() {
                                selectTaskInputs = document.getElementsByClassName('transaction-selection')
                                for (var i = 0; i < selectTaskInputs.length; i++) {
                                    selectTaskInputs[i].checked = true;
                                }
                                document.getElementById('flag_all').checked = true
                            }
                            function deselectAllContacts() {
                                selectTaskInputs = document.getElementsByClassName('transaction-selection')
                                for (var i = 0; i < selectTaskInputs.length; i++) {
                                    selectTaskInputs[i].checked = false;
                                }
                                document.getElementById('flag_all').checked = false
                                document.getElementById('transaction-bulk-action-container').classList.add('d-none')
                                document.getElementById('transaction-view-container').classList.remove('d-none')
                                document.getElementById('transaction-view-container-1').classList.remove('d-none')
                            }
                        </script>
                    </div>
                    {% if permission|check_permission:'edit' %}
                        <button class="py-1 rounded-1 btn btn-sm btn-light-warning fw-bold mt-2 mb-1" onclick="check_permission_action(event, 'edit')" name="bulk_duplicate" type="submit" form="order-form">
                            {% if LANGUAGE_CODE == 'ja'%}
                            複製
                            {% else %}
                            Duplicate
                            {% endif %}
                        </button>
                        {% endif %}
                    {% if permission|check_permission:'archive' %}
                    <button class="py-1 mt-2 mb-1 rounded-1 btn btn-sm btn-light-success fw-bold me-2" onclick="check_permission_action(event, 'archive', 'manage_restore_bulk')">
                        {% if LANGUAGE_CODE == 'ja'%}
                        有効化
                        {% else %}
                        Activate
                        {% endif %}
                    </button>
                    <button class="py-1 mt-2 mb-1 btn btn-sm btn-light-danger rounded-1 fw-bold me-2" onclick="check_permission_action(event, 'archive', 'manage_delete_bulk')">
                        {% if LANGUAGE_CODE == 'ja'%}
                        アーカイブ
                        {% else %}
                        Archive 
                        {% endif %}
                    </button>
                    {% endif %}

                    <script>
                        document.body.addEventListener("hideTaskBulkActionBtn", function(evt){
                            document.getElementById('transaction-bulk-action-container').classList.add('d-none')
                            document.getElementById('transaction-view-contianer').classList.remove('d-none')
                        })
                    </script>
                </div>

                <div class="d-flex w-50 align-items-center justify-content-end">
                    {% if permission|check_permission:'edit' %}
                    <div class="{% include "data/utility/table-button.html" %}">
                        <button class="{% include "data/utility/gray-header-button.html" %} create-view-settings-button" type="button"
                            hx-get="{% url 'shopturbo_load_drawer' %}" 
                            hx-vals='{"module":"{{menu_key}}"}'
                            hx-trigger="click"
                            onclick="fillItemExportIds(this)"
                            hx-target="#manage-contacts-view-settings-drawer"

                            >
                            <span class="svg-icon svg-icon-4">
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-upload" viewBox="0 0 16 16">
                                    <path d="M.5 9.9a.5.5 0 0 1 .5.5v2.5a1 1 0 0 0 1 1h12a1 1 0 0 0 1-1v-2.5a.5.5 0 0 1 1 0v2.5a2 2 0 0 1-2 2H2a2 2 0 0 1-2-2v-2.5a.5.5 0 0 1 .5-.5"/>
                                    <path d="M7.646 1.146a.5.5 0 0 1 .708 0l3 3a.5.5 0 0 1-.708.708L8.5 2.707V11.5a.5.5 0 0 1-1 0V2.707L5.354 4.854a.5.5 0 1 1-.708-.708z"/>
                                </svg>
                            </span>
                            <span class="">
                                {% if LANGUAGE_CODE == 'ja'%}
                                エクスポート
                                {% else %}
                                Export
                                {% endif %}
                            </span>

                        </button> 
                        
                    </div>
                    {% endif %}
                </div>
            </div>

        </div>
    </div>
    {% comment %} End of Views {% endcomment %}



    <div class="mb-2 d-none" id="d-select-additional-options">
        <div class="px-5 pt-4 pb-3 text-center rounded status bg-light-dark">
            
            {% if LANGUAGE_CODE == 'ja'%}
            このページの全てのレコードが選択されました。 
            {% else %}
            All records on this page are selected. 
            {% endif %}

            <a onclick="toggleText()" 
                class="btn btn-dark" 
                data-bs-toggle="collapse" 
                id="select-additional-options-toggle" 
                role="button" 
                aria-expanded="false" 
                aria-controls="collapseExample">
                {% if LANGUAGE_CODE == 'ja'%}
                全てのレコードを選択する
                {% else %}
                Select all records
                {% endif %}

                </a>
        </div>
    </div>


    <form method="POST" id="order-form">
        {% csrf_token %}
        
        <div class="pt-0 table-responsive">
            <table class="{% include "data/utility/table.html" %} inventory-transaction-table">
                <thead class="{% include "data/utility/table-header.html" %} position-sticky">
                    <tr class="align-middle">
                        
                        {% for shopturbo_inventory_column in shopturbo_transaction_columns %}
                            <th {% if shopturbo_inventory_column == 'checkbox' %} 
                                class="{% include "data/utility/column-checkbox-size.html" %}" 
                                {% elif shopturbo_inventory_column == 'transaction_id' %}
                                class="{% include "data/utility/column-id-size.html" %}"
                                {% else %} 
                                class="" 
                                {% endif %}>
                                {% if shopturbo_inventory_column != 'checkbox' %}
                                    {% if shopturbo_inventory_column|search_custom_field_object_inventory_transaction:request %}
                                        {% with channel_column=shopturbo_inventory_column|search_custom_field_object_inventory_transaction:request %}
                                            {{channel_column.name|display_column_orders:request}}
                                        {% endwith %}
                                    {%else%}
                                        {% with column_value=shopturbo_inventory_column|display_column_inventory_transaction:request %}
                                            {{column_value}}
                                        {% endwith %}
                                    {%endif%}
                                
                                {% endif %}
                            </th>


                        {% endfor %}

                    </tr>
                </thead>
                <tbody class="fs-6">

                    {% for transaction in shopturbo_transactions %}
                    <tr>
                        {% for shopturbo_transaction_column in shopturbo_transaction_columns %}
                            {% include 'data/shopturbo/inventory-transaction-row-partial.html' with transaction=transaction row_type=shopturbo_transaction_column %}
                        {% endfor %}
                    </tr>
                    {% endfor %}

                </tbody> 
            </table>

            <input id="flag_all" type="checkbox" name='flag_all' class="flag_all" hidden>
            
            <div class="modal fade" tabindex="-1" id="manage_delete_bulk">
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="pb-0 border-0 modal-header justify-content-end">
                            <div class="btn btn-icon btn-sm btn-active-light-primary ms-2" data-bs-dismiss="modal" aria-label="Close">
                            </div>
                        </div>
                        <div class="pb-0 modal-body">
                            <div class="text-center mb-13">
                                <h3 class="modal-title">
                                    
                                    {% if LANGUAGE_CODE == 'ja'%}
                                    一括アーカイブの確認
                                    {% else %}
                                    Bulk Archive Confirmation
                                    {% endif %}
                                
                                </h3>
                            </div>
                            <div class="border-bottom">
                                <div class="{% include "data/utility/form-div.html" %}">
                                    <label class="mb-2 d-flex align-items-center fs-6 fw-bold">
                                        <span class="">
                                            {% if LANGUAGE_CODE == 'ja'%}
                                            選択されたレコードをアーカイブしてもよろしいですか?
                                            {% else %}
                                            Are you sure to archive selected records?
                                            {% endif %}
                                        </span>
                                    </label>
                            
                                </div>
                            </div>
                        </div>
                        
        
                        <div class="border-0 modal-footer">
                            <button name="bulk_delete_items" type="submit" class="btn btn-danger">
                                
                                {% if LANGUAGE_CODE == 'ja'%}
                                    アーカイブ
                                    {% else %}
                                    Archive
                                    {% endif %}
                                </button>
                            
                            </button>
                            <a data-bs-dismiss="modal" class="btn btn-dark">
                                {% if LANGUAGE_CODE == 'ja'%}
                                キャンセル
                                {% else %}
                                Cancel
                                {% endif %}
                            </a>
                        </div>
                    </div>
                </div>
            </div> 

            <div class="modal fade" tabindex="-1" id="manage_restore_bulk">
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="pb-0 border-0 modal-header justify-content-end">
                            <div class="btn btn-icon btn-sm btn-active-light-primary ms-2" data-bs-dismiss="modal" aria-label="Close">
                            </div>
                        </div>
                        <div class="pb-0 modal-body">
                            <div class="text-center mb-13">
                                <h3 class="modal-title">
                                    
                                    {% if LANGUAGE_CODE == 'ja'%}
                                    一括有効化の確認
                                    {% else %}
                                    Bulk Activate Confirmation
                                    {% endif %}
                                
                                </h3>
                            </div>
                            <div class="border-bottom">
                                <div class="{% include "data/utility/form-div.html" %}">
                                    <label class="mb-2 d-flex align-items-center fs-6 fw-bold">
                                        <span class="">
                                            {% if LANGUAGE_CODE == 'ja'%}
                                            これらの取引を有効にしてもよろしいですか?
                                            {% else %}
                                            Are you sure to activate these transactions?
                                            {% endif %}
                                        </span>
                                    </label>
                               
                                </div>
                            </div>
                        </div>
                        
        
                        <div class="border-0 modal-footer">
                            <button name="bulk_restore_items" type="submit" class="btn btn-success">
                                {% if LANGUAGE_CODE == 'ja'%}
                                有効化
                                {% else %}
                                Activate
                                {% endif %}
                            </button>
                            <a data-bs-dismiss="modal" class="btn btn-dark">
                                {% if LANGUAGE_CODE == 'ja'%}
                                キャンセル
                                {% else %}
                                Cancel
                                {% endif %}
                            </a>
                        </div>
                    </div>
                </div>
            </div> 

        </div>
    </form>

    <div class="{% include "data/utility/pagination.html" %}">
        {% if LANGUAGE_CODE == 'ja'%}
        {{paginator_item_begin}}–{{paginator_item_end}} の {{paginator.count}} 件
        {% else %}
        Viewing {{paginator_item_begin}}–{{paginator_item_end}} of {{paginator.count}} results
        {% endif %}
        <div>
            {% if page_content.has_previous %}     
                <a class="bg-gray-100 btn w-100px" href="{% host_url 'load_object_page' menu_key object_type|object_type_slug:request host 'app' %}?page=1&{% query_transform %}">{% translate_lang "First" LANGUAGE_CODE %}</a>
                <a class="bg-gray-100 btn w-100px" href="{% host_url 'load_object_page' menu_key object_type|object_type_slug:request host 'app' %}?page={{ page_content.previous_page_number }}&{% query_transform %}">{% translate_lang "Previous" LANGUAGE_CODE %}</a>
            {% endif %}
                    
            {% if page_content.has_next %}
                <a class="bg-gray-100 btn w-100px" href="{% host_url 'load_object_page' menu_key object_type|object_type_slug:request host 'app' %}?page={{ page_content.next_page_number }}&{% query_transform %}">{% translate_lang "Next" LANGUAGE_CODE %}</a>
                <a class="bg-gray-100 btn w-100px" href="{% host_url 'load_object_page' menu_key object_type|object_type_slug:request host 'app' %}?page={{ page_content.paginator.num_pages }}&{% query_transform %}">{% translate_lang "Last" LANGUAGE_CODE %}</a>
            {% endif %}
        </div>
    </div>

</div>

{% if transaction_id %}
<a id="selected_show_items-transaction" class="d-none text-center mb-0 text-dark text-hover-primary fw-bolder cursor-pointer manage-full-wizard-button" 
    hx-get="{% url 'shopturbo_load_drawer' %}" 
    hx-vals = '{"drawer_type":"inventory-transaction-manage", "transaction_id":"{{transaction_id}}", "module":"{{menu_key}}"}'
    hx-target="#manage-full-drawer-content"
    hx-indicator=".loading-drawer-spinner,#shopturbo-drawer-content"  
    hx-trigger="click"
    hx-on::before-request="document.getElementById('manage-full-drawer-content').innerHTML = '';"
></a>
<script>
    $(document).ready(function() {
        setTimeout(function() {
            document.getElementById('selected_show_items-transaction').click();
        }, 0);
    });
</script>
{% endif %}


<script>
    // Function to load script dynamically
    function loadScript(url, callback) {
        var script = document.createElement('script');
        script.type = 'text/javascript';
        script.src = url;
        script.onload = callback;
        script.onerror = function() {
            console.error('Failed to load script:', url);
        };
        document.head.appendChild(script);
    }

    // Check if jQuery is loaded, if not, load it
    function ensureJQuery(callback) {
        if (typeof window.jQuery === 'undefined') {
            console.log('jQuery not loaded, loading now...');
            loadScript('https://cdn.jsdelivr.net/jquery/latest/jquery.min.js', function() {
                console.log('jQuery loaded successfully');
                if (callback) callback();
            });
        } else {
            console.log('jQuery already loaded');
            if (callback) callback();
        }
    }

    // Check if DataTable is loaded, if not, load it
    function ensureDataTableScripts(callback) {
        if (typeof $.fn.DataTable === 'undefined') {
            console.log('DataTable not loaded, loading scripts...');
            loadScript('https://cdn.datatables.net/1.13.7/js/jquery.dataTables.min.js', function() {
                console.log('DataTable core loaded');
                loadScript('https://cdn.datatables.net/fixedcolumns/4.3.0/js/dataTables.fixedColumns.min.js', function() {
                    console.log('DataTable FixedColumns loaded');
                    if (callback) callback();
                });
            });
        } else {
            console.log('DataTable already loaded');
            if (callback) callback();
        }
    }

    var table = null;
    var requestNum = {% if shopturbo_items|length == 0 %}1{% else %}{{shopturbo_items|length}}{% endif %}
    
    // Make sure jQuery and DataTables are properly initialized
    function ensureDataTable(callback) {
        // First ensure jQuery is loaded
        ensureJQuery(function() {
            // Then ensure DataTable scripts are loaded
            ensureDataTableScripts(function() {
                if (callback && typeof callback === 'function') {
                    callback();
                }
            });
        });
    }

    $(document).ready(function() {
        ensureDataTable(function() {
            table = $(".inventory-transaction-table").DataTable({
                responsive: true,
                scrollX:        true,
                scrollY:        "75vh",
                scrollCollapse: true,
                fixedColumns:   {
                    left: 2
                },
                fixedHeader:   true,
                ordering: false,
                searching: false,  // Hide the search bar
                paging: false,      // Hide pagination
                info: false,        // Hide the information text
                language: {
                    emptyTable: "{% translate_lang 'No data available in table' LANGUAGE_CODE %}"
                }
            });
        });
    });

    // Keep your existing click handler
    $(document).ready(function() {
        setTimeout(function() {
            document.getElementById('selected_show_items')?.click();
        }, 0);
    });

    function toggleText() {
        var x = document.getElementById("select-additional-options-toggle");
        var toggle_data = x.getAttribute('toggle-data')
        if (toggle_data !== "true") {
                
                {% if LANGUAGE_CODE == 'ja'%}
                x.innerHTML = "選択を解除";
                {% else %}
                x.innerHTML = "Clear All";
                {% endif %}

          $(".flag_all").each(function(index, element) {
                element.value = true
            });

            x.setAttribute('toggle-data',"true")

        } else {

            x.setAttribute('toggle-data',"false")

            
            $('input[type=checkbox]').prop('checked', false);

            //Hide
            var element_select_options = document.getElementById("d-select-additional-options");
            element_select_options.classList.add("d-none")

            x.innerHTML = "Select All {{paginator.count}} contacts in this sections";
            
            $(".flag_all").each(function(index, element) {
                element.value = false
            });

        }
      }



    function select_all() {
        $('#selectAll').prop('checked', !$('#selectAll').prop('checked'));
        if ($('#selectAll').prop('checked') == true) {
            //downloadelem = document.getElementById("csv_download");
            //downloadelem.classList.add("disabled");
            
            $('input[type=checkbox]').prop('checked', false);

            var element_select_options = document.getElementById("d-select-additional-options");
            element_select_options.classList.add("d-none")

        } else {
            //downloadelem = document.getElementById("csv_download");
            //downloadelem.classList.remove("disabled");

            $('input[type=checkbox]').prop('checked', true);

            var element_select_options = document.getElementById("d-select-additional-options");
            element_select_options.classList.remove("d-none")

        }
    }


    var contactChecked = {}
    var first_id_check_box = null;
    const checking_checkbox = (elem,event) => {
        if (elem.checked){
            contactChecked[elem.id] = elem.id;

            if (event.shiftKey) {

                if (first_id_check_box){
                    
                    var idList=[]
                    var all_checkboxes = document.querySelectorAll('input[type="checkbox"]');
                    all_checkboxes.forEach(function(checkbox) {
                        if (checkbox.id != "selectAll") {
                            idList.push(checkbox.id);
                        }
                    });


                    // Find the index of id1 and id2 in the idList
                    var index_first_id = idList.indexOf(first_id_check_box);
                    var index_shift_pressed_id = idList.indexOf(elem.id);
                    // If either id1 or id2 is not found in the idList, or they are next to each other, return null
                    if (index_first_id === -1 || index_shift_pressed_id === -1 || Math.abs(index_first_id - index_shift_pressed_id) === 1) {

                    } else {
                        // Determine the smaller and larger index
                        var minIndex = Math.min(index_first_id, index_shift_pressed_id);
                        var maxIndex = Math.max(index_first_id, index_shift_pressed_id);
                        // Extract the IDs between the indices
                        var idsBetween = idList.slice(minIndex + 1, maxIndex);

                        if (idsBetween){
                            
                            idsBetween.forEach(function(id) {
                                var checkbox = document.getElementById(id);
                                if (checkbox) {
                                    checkbox.checked = true;
                                }
                            });
                            
                        }

                    }

                }

            }
            else{
                var checkboxes = document.querySelectorAll('input[type="checkbox"]:checked');
                
                first_id_check_box = elem.id
            }
        }
        else{
            delete contactChecked[elem.id];
            if (Object.keys(contactChecked).length == 0){
                
                var checkboxes = document.querySelectorAll('input[type="checkbox"]:checked');
            }
        }
    }
    
    {% include "data/common/open-drawer.js" %}
</script>


<script>
    // Keyboard shortcut for opening the 'Create New' drawer (n key)
    document.addEventListener('keydown', function(event) {
        // Ignore if input, textarea, or contenteditable is focused
        const active = document.activeElement;
        if (active && (active.tagName === 'INPUT' || active.tagName === 'TEXTAREA' || active.isContentEditable)) {
            return;
        }

        // Check if the key pressed is 'n'
        if (event.key.toLowerCase() === 'n') {
            event.preventDefault(); // Prevent default 'n' behavior
            
            // Find the 'Create New' button (adjust selector if needed)
            const newButton = document.querySelector('.view_form_trigger'); 
            
            if (newButton) {
                newButton.click(); // Simulate click to open the drawer
            }
        }
    });
</script>

<script>
    function check_permission_action(event, permission_type, ...args){

        let source = args.length > 0 ? args[0] : null;

        const checkInputs = document.querySelectorAll('.check_input:checked');

        let members = "{{group_members}}"
        members = members.split(',')
        const user_id = '{{request.user.id}}'
        const permission = '{{permission}}';
        const permission_list = permission.split('|');
        let scope = ''
        permission_list.forEach(p => {
            if (p.includes(permission_type)) {
                p_split = p.split('_');
                scope = p_split[0]
            }
        })

        let msg = '';
        let denied = false;
        for (let i = 0; i < checkInputs.length; i++) {
            var owner_id = checkInputs[i].dataset.owner;
            if (owner_id){
                if (scope == 'user'){
                    if (owner_id.toString() !== user_id.toString()) {
                        {% if LANGUAGE_CODE == 'ja' %}
                        msg = "操作が拒否されました。自分のアイテムのみ編集または削除することができます。";
                        {% else %}
                        msg = "Action denied. You are only allowed to edit or delete your own items.";
                        {% endif %}                    
                        checkInputs[i].click()
                        denied = true;
                    }
                } else if (scope == 'team'){
                    if (!members.includes(owner_id.toString())) {
                        {% if LANGUAGE_CODE == 'ja' %}
                        msg = "操作が拒否されました。自分または自分のチームに割り当てられたアイテムのみ編集または削除できます。";
                        {% else %}
                        msg = "Action denied. You can only edit or delete items assigned to you or your team.";
                        {% endif %}
                        checkInputs[i].click()
                        denied = true;
                    }
                } 
            }
        }
        if (denied) {
            event.preventDefault();
            event.stopImmediatePropagation();
            document.getElementById('permissionActionWarning').innerHTML = msg;
            setTimeout(() => {
                document.getElementById('permissionActionWarning').innerHTML = '';
            }, 4000);
            msg = ''
        } else if (source){
            const modalEl = document.getElementById(source);
            const modal = bootstrap.Modal.getOrCreateInstance(modalEl);
            modal.show();
        }

    }
</script>

{% include 'data/javascript/toggleSearch.html' %} 

{% endif %}

{% endblock %}

