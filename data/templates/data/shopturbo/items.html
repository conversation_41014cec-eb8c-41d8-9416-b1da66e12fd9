{% extends 'base.html' %}
{% load i18n %}
{% load custom_tags %}
{% load humanize %}
{% load hosts %}
{% block content %}

{% include "data/utility/table-css.html" %}

{% include 'data/static/tootip-search-wrapper.html' %}
<div class="d-flex d-flex align-items-center mb-2" style="background-color: #FAFAFA !important;">
    {% include 'data/common/module-header-tabs.html' %}

    <div class="w-50 d-flex justify-content-end tw-mr-5" id="view-container">
        {% if permission|check_permission:'edit' %}
            <div class="{% include "data/utility/table-button.html" %}">
                <button id='view-sync-items-action-drawer' type="button" class="max-md:tw-hidden w-125px align-items-center d-flex svg-icon-gray-600 btn py-1 rounded-1 tw-bg-gray-200 hover:tw-bg-gray-300 justify-content-center px-1 object-action-drawer-button"
                    hx-get="{% url 'shopturbo_load_drawer' %}" 
                    hx-trigger="click"
                    onclick="fillActionItemIds(this),check_permission_action(event, 'edit')"
                    hx-target="#object-action-drawer-content"
                    hx-indicator=".loading-drawer-spinner,.view-form"
                    >
                    <span class="svg-icon svg-icon-4">
                        <svg width="14" height="16" viewBox="0 0 14 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M8.49968 1.33301H4.66264C4.54298 1.33301 4.48316 1.33301 4.43034 1.35123C4.38363 1.36734 4.34109 1.39363 4.30579 1.4282C4.26587 1.4673 4.23912 1.52081 4.18561 1.62783L1.38561 7.22783C1.25782 7.4834 1.19393 7.61119 1.20927 7.71506C1.22268 7.80576 1.27285 7.88694 1.34798 7.93949C1.43403 7.99967 1.5769 7.99967 1.86264 7.99967H5.99968L3.99968 14.6663L12.1284 6.23655C12.4027 5.95214 12.5398 5.80994 12.5478 5.68826C12.5548 5.58265 12.5112 5.48 12.4303 5.4117C12.3371 5.33301 12.1396 5.33301 11.7445 5.33301H6.99968L8.49968 1.33301Z" stroke="#3B3B3F" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                    </span>
                    <span class="">
                        {% if LANGUAGE_CODE == 'ja'%}
                        アクション
                        {% else %}
                        Action
                        {% endif %}
                    </span>
                </button>
            </div>

            <script>
                {% if open_drawer == 'action_drawer_history' %}
                    $(document).ready(function() {
                        setTimeout(function() {
                            document.getElementById('view-sync-items-action-drawer').click()
                        }, 1000)
                    })
                {% endif %}
                function fillActionItemIds(elm) {
                    // Now set the hx-vals attribute with the updated account IDs
                    let checked = false;
                    const checkedAll = document.querySelector(".flag_all_item").checked;
                    if (checkedAll) {
                        checked = true;
                    }
                    elm.setAttribute('hx-vals', 'js:{"drawer_type":"items", "section":"action_history", "action_tab":"action", "item_ids": getSelectedItems(), "module": "{{menu_key}}", "is_object_action": '+ true +', "open_drawer": "{{open_drawer}}", "view_filter_id": "{{view_filter.id}}", "flag_all": '+ checked +'}');
                }
            </script>
        {% endif %}

        <div class="{% include "data/utility/header-action-button.html" %}">
            <button class="btn tw-font-[500] tw-rounded-l-lg tw-rounded-r-none tw-border-0 tw-bg-gray-200 hover:tw-bg-gray-300 max-md:tw-hidden w-125px align-items-center d-flex svg-icon-gray-600 py-1 justify-content-center px-1 create-view-settings-button" type="button"
                hx-get="{% url 'shopturbo_load_drawer' %}" 
                hx-trigger="click"
                onclick="fillItemExportIds(this)"
                hx-target="#manage-contacts-view-settings-drawer"
                hx-indicator=".loading-drawer-spinner,.view-form"
                >
                <span class="svg-icon svg-icon-4">
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-upload" viewBox="0 0 16 16">
                        <path d="M.5 9.9a.5.5 0 0 1 .5.5v2.5a1 1 0 0 0 1 1h12a1 1 0 0 0 1-1v-2.5a.5.5 0 0 1 1 0v2.5a2 2 0 0 1-2 2H2a2 2 0 0 1-2-2v-2.5a.5.5 0 0 1 .5-.5"/>
                        <path d="M7.646 1.146a.5.5 0 0 1 .708 0l3 3a.5.5 0 0 1-.708.708L8.5 2.707V11.5a.5.5 0 0 1-1 0V2.707L5.354 4.854a.5.5 0 1 1-.708-.708z"/>
                    </svg>
                </span>
                <span class="">
                    {% if LANGUAGE_CODE == 'ja'%}
                    エクスポート
                    {% else %}
                    Export
                    {% endif %}
                </span>
            </button>
            {% if permission|check_permission:'edit' %}
                <button id='view-sync-items' type="button" class="{% include "data/utility/import-button.html" %}"
                    hx-get="{% url 'shopturbo_load_drawer' %}?module={{menu_key}}&object_type={{object_type}}"
                    hx-trigger="click"
                    onclick="fillItemIds(this)"
                    hx-indicator=".loading-drawer-spinner,.view-form"
                    hx-target="#manage-contacts-view-settings-drawer"
                    >
                    <span class="svg-icon svg-icon-4">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-download" viewBox="0 0 16 16">
                            <path d="M.5 9.9a.5.5 0 0 1 .5.5v2.5a1 1 0 0 0 1 1h12a1 1 0 0 0 1-1v-2.5a.5.5 0 0 1 1 0v2.5a2 2 0 0 1-2 2H2a2 2 0 0 1-2-2v-2.5a.5.5 0 0 1 .5-.5"/>
                            <path d="M7.646 11.854a.5.5 0 0 0 .708 0l3-3a.5.5 0 0 0-.708-.708L8.5 10.293V1.5a.5.5 0 0 0-1 0v8.793L5.354 8.146a.5.5 0 1 0-.708.708z"/>
                        </svg>
                    </span>
                    <span class="">
                        {% if LANGUAGE_CODE == 'ja'%}
                        インポート
                        {% else %}
                        Import
                        {% endif %}
                    </span>
                </button>
            {% endif %}
        </div>

        <div class="btn-group tw-h-[32px]">
            <button class="tw-w-[100px] align-items-center d-flex btn btn-primary btn-md shopturbo-create-wizard-button view_form_trigger py-1" type="button"
                hx-get="{% url 'shopturbo_load_drawer' %}" 
                hx-vals = '{"drawer_type":"items", "view_id": "{{view_filter.view.id}}", "module": "{{menu_key}}", "object_type": "{{object_type}}", "page": "{{page}}", "set_id": "{{set_id}}"}'
                hx-target="#shopturbo-create-drawer-content"
                hx-indicator=".loading-drawer-spinner,#shopturbo-create-drawer-content"
                style="border-radius: 0.475rem 0 0 0.475rem;"
                >
                <span class="svg-icon svg-icon-4">
                    <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M8.00065 3.33301V12.6663M3.33398 7.99967H12.6673" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                    </svg>
                </span>
        
                <span class="fs-7 ps-1 fw-bolder w-85px">
                    {% if LANGUAGE_CODE == 'ja'%}
                    新規
                    {% else %}
                    New
                    {% endif %}
                </span>
            </button> 
            <button type="button" 
                class="tw-w-[30px] align-items-center d-flex btn btn-primary btn-md dropdown-toggle py-1 tw-pl-2 dropdown-toggle-split" 
                data-bs-toggle="dropdown" 
                aria-expanded="false"
                style="border-radius: 0 0.475rem 0.475rem 0;border-left: 1px solid #dee2e6;"
            >
                <span class="svg-icon svg-icon-4">
                    <svg width=24 height=24 xmlns="http://www.w3.org/2000/svg" viewBox="0 0 320 512" fill="currentColor">
                        <path d="M137.4 374.6c12.5 12.5 32.8 12.5 45.3 0l128-128c9.2-9.2 11.9-22.9 6.9-34.9s-16.6-19.8-29.6-19.8L32 192c-12.9 0-24.6 7.8-29.6 19.8s-2.2 25.7 6.9 34.9l128 128z"/>
                    </svg>
                </span>
            </button>
            <ul class="dropdown-menu tw-max-w-[180px] tw-max-h-[300px] tw-overflow-y-scroll">
                <li>
                    <button class="dropdown-item tw-text-ellipsis tw-overflow-hidden shopturbo-manage-wizard-button" type="button"
                        hx-get="{% url 'shopturbo_load_drawer' %}" 
                        hx-vals = '{"drawer_type":"barcode", "view_id": "{{view_id}}", "section": "items-query-barcode", "module": "{{menu_key}}"}'
                        hx-target="#shopturbo-drawer-content"
                        hx-indicator=".loading-drawer-spinner,#shopturbo-drawer-content"
                        style="border-radius: 0.475rem 0 0 0.475rem;"
                        >
                        {% if LANGUAGE_CODE == 'ja'%}バーコード/QRコードで検索{% else %}Bar/QR Code Search{% endif %}
                    </button>
                </li>
                <li>
                    <button class="dropdown-item tw-text-ellipsis tw-overflow-hidden shopturbo-create-wizard-button" type="button"
                        hx-get="{% url 'shopturbo_load_drawer' %}" 
                        hx-vals = '{"drawer_type":"upload_photos", "view_id": "{{view_id}}", "module": "{{menu_key}}"}'
                        hx-target="#shopturbo-create-drawer-content"
                        hx-indicator=".loading-drawer-spinner,#shopturbo-create-drawer-content"
                        style="border-radius: 0.475rem 0 0 0.475rem;"
                        >
                    {% if LANGUAGE_CODE == 'ja'%}
                        画像をアップロード
                    {% else %}
                        Upload Photos
                    {% endif %}
                    </button>
                </li>
                {% for set in property_sets %}
                <li>
                    <button class="dropdown-item tw-text-ellipsis tw-overflow-hidden shopturbo-create-wizard-button" type="button"
                        hx-get="{% url 'shopturbo_load_drawer' %}" 
                        hx-vals = '{"drawer_type":"items", "view_id": "{{view_id}}", "set_id": "{{set.id}}", "module": "{{menu_key}}"}'
                        hx-target="#shopturbo-create-drawer-content"
                        hx-indicator=".loading-drawer-spinner,#shopturbo-create-drawer-content"
                        style="border-radius: 0.475rem 0 0 0.475rem;"
                        >
                        {% if set.name %}
                            {{ set.name}}
                        {% else %}
                            {% if LANGUAGE_CODE == 'ja' %}デフォルト{% else %}Default{% endif %} 
                        {% endif %}
                    </button>
                </li>
                {% endfor %}
            </ul>
        </div>
    </div>
</div>
{% if permission == 'hide' %}
    {% include 'data/static/no-access-page.html' %}
{% else %}

{% include "data/common/advance_search/advance-search-style.html" %}

{% if permission|check_permission:'edit' %}
    <div id='modal-load'
        hx-vals='{"object_type": "{{object_type}}","module": "{{menu_key}}","view_id": "{{view_id}}"}'
        hx-get="{% url 'get_bulk_update_properties' %}" 
        hx-trigger='load'
        hx-target="this">
    </div>
{% endif %}

<div class="w-100 tw-pl-2">
    {% include 'data/common/permission-action-warning-message.html' %}
    {% comment %} Views Part {% endcomment %}
    <div class="{% include "data/utility/tab-pane.html" %} pt-5" role="tabpanel" style="z-index:4 !important;">
        <div class="{% include "data/utility/table-nav.html" %}">
            <div class="{% include "data/utility/table-content.html" %}" id="view-container-1">
                {% comment %} Desktop {% endcomment %}
                <div class="max-md:tw-hidden w-75 d-flex align-items-center">
                    <div class="nav-item fs-6 text-gray-900">
                        <div class="d-flex align-items-center justify-content-center">
                            <button type="button" class="align-items-center justify-content-center d-flex btn text-gray-600 bg-transparent text-hover-primary tw-px-[5px] create-view-settings-button mb-2 tw-w-[30px]"
                                style="height: 26px;"
                                hx-vals='{"drawer_type":"shopturbo-view-settings","page": "items", "module": "{{menu_key}}"}'
                                hx-get="{% url 'shopturbo_load_drawer' %}" 
                                hx-indicator=".loading-drawer-spinner,.view-form"
                                hx-target="#manage-contacts-view-settings-drawer"
                                hx-trigger="click"
                                hx-swap="innerHTML"
                            >
                                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="currentColor" class="bi bi-plus" viewBox="0 0 16 16">
                                    <path d="M8 4a.5.5 0 0 1 .5.5v3h3a.5.5 0 0 1 0 1h-3v3a.5.5 0 0 1-1 0v-3h-3a.5.5 0 0 1 0-1h3v-3A.5.5 0 0 1 8 4z"/>
                                </svg>
                            </button>
                        </div>
                    </div>
                    
                    {% include "data/projects/partial-dropdown-view-menu.html" %}
                    <div class="{% include "data/utility/view-menu-nav-item.html" %}">
                        <a class="{% include "data/utility/view-menu-default.html" %}" 
                            type="button"
                            href = "{% host_url 'load_object_page' menu_key object_type|object_type_slug:request host 'app' %}">
                            <span class="svg-icon svg-icon-muted svg-icon-2"><svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M22 12C22 17.5 17.5 22 12 22C6.5 22 2 17.5 2 12C2 6.5 6.5 2 12 2C17.5 2 22 6.5 22 12ZM12 6C8.7 6 6 8.7 6 12C6 15.3 8.7 18 12 18C15.3 18 18 15.3 18 12C18 8.7 15.3 6 12 6Z" fill="currentColor"/>
                                </svg>
                            </span>
                        </a>
                        {% if not view_filter.view.title %}
                        <div class="w-20px nav-item justify-content-center d-flex fs-6 text-gray-900">
                            <button type="button" class="{% include "data/utility/view-plus-link.html" %} create-view-settings-button"
                                hx-vals='{"drawer_type":"shopturbo-view-settings","page": "items", "view_id":"{{view_filter.view.id}}", "module": "{{menu_key}}"}'
                                hx-get="{% url 'shopturbo_load_drawer' %}" 
                                hx-indicator=".loading-drawer-spinner,.view-form"
                                hx-target="#manage-contacts-view-settings-drawer"
                                hx-trigger="click"
                                hx-swap="innerHTML"
                                >
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-three-dots-vertical" viewBox="0 0 16 16">
                                    <path d="M9.5 13a1.5 1.5 0 1 1-3 0 1.5 1.5 0 0 1 3 0zm0-5a1.5 1.5 0 1 1-3 0 1.5 1.5 0 0 1 3 0zm0-5a1.5 1.5 0 1 1-3 0 1.5 1.5 0 0 1 3 0z"/>
                                </svg>
                            </button>
                        </div>
                        {% endif %}
                    </div>

                    {% include 'data/projects/partial-view-menu.html' %}

                </div>

                {% comment %} Mobile View {% endcomment %}
                <div class="tw-hidden max-md:tw-flex tw-w-1/2">
                    <div class="d-flex align-items-center">
                        <!-- Example split danger button -->
                        <div class="btn-group mb-2">
                            <button type="button" class="tw-max-w-[90px] align-items-center d-flex btn bg-white border btn-md tw-px-[10px] create-view-settings-button"
                                style="height: 26px; border-radius: 0.475rem 0 0 0.475rem;"
                                hx-vals='{"drawer_type":"shopturbo-view-settings","page": "items", "view_id":"{{view_filter.view.id}}", "module": "{{menu_key}}"}'
                                hx-get="{% url 'shopturbo_load_drawer' %}" 
                                hx-indicator=".loading-drawer-spinner,.view-form"
                                hx-target="#manage-contacts-view-settings-drawer"
                                hx-trigger="click"
                                hx-swap="innerHTML"
                            >
                                <span class="fs-7 ps-1 fw-bolder tw-text-ellipsis tw-overflow-hidden tw-text-nowrap">
                                    {% if view_filter.view.title %}
                                        {{ view_filter.view.title }}
                                    {% else %}
                                        {% if LANGUAGE_CODE == 'ja'%}ビュー{% else %}View{% endif %}
                                    {% endif %}
                                </span>
                            </button>
                            <button type="button" 
                                class="tw-w-[30px] align-items-center d-flex btn btn-white border btn-md  dropdown-toggle py-1 tw-pl-2 dropdown-toggle-split" 
                                data-bs-toggle="dropdown" 
                                aria-expanded="false"
                                style="height: 26px; border-radius: 0 0.475rem 0.475rem 0;"
                            >
                                <span class="svg-icon svg-icon-4">
                                    <svg width=24 height=24 xmlns="http://www.w3.org/2000/svg" viewBox="0 0 320 512" fill="currentColor">
                                        <path d="M137.4 374.6c12.5 12.5 32.8 12.5 45.3 0l128-128c9.2-9.2 11.9-22.9 6.9-34.9s-16.6-19.8-29.6-19.8L32 192c-12.9 0-24.6 7.8-29.6 19.8s-2.2 25.7 6.9 34.9l128 128z"/>
                                    </svg>
                                </span>
                            </button>
                            <ul class="dropdown-menu tw-max-w-[180px] tw-max-h-[300px] tw-overflow-y-scroll">
                                <li><a class="dropdown-item tw-text-ellipsis tw-overflow-hidden" href="{% host_url 'load_object_page' menu_key object_type|object_type_slug:request host 'app' %}">{% if LANGUAGE_CODE == 'ja' %}デフォルト{% else %}Default View{% endif %}</a></li>
                                <div class="dropdown-divider"></div>
                                {% for view in views %}
                                    {% if view.title %}
                                        <li><a class="dropdown-item tw-text-ellipsis tw-overflow-hidden" href="{% host_url 'load_object_page' menu_key object_type|object_type_slug:request host 'app' %}?view_id={{view.id}}">{{view.title}}</a></li>
                                    {% endif %}
                                {% endfor %}
                            </ul>
                        </div>
                        <div class="fs-6 text-gray-900 mb-2">
                            <button type="button" class="tw-border-0 tw-bg-transparent tw-text-gray-900 create-view-settings-button" 
                                hx-vals='{"drawer_type":"shopturbo-view-settings","page": "items", "module": "{{menu_key}}"}'
                                hx-get="{% url 'shopturbo_load_drawer' %}" 
                                hx-indicator=".loading-drawer-spinner,.view-form"
                                hx-target="#manage-contacts-view-settings-drawer"
                                hx-trigger="click"
                                hx-swap="innerHTML">
                                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="currentColor" class="bi bi-plus" viewBox="0 0 16 16">
                                    <path d="M8 4a.5.5 0 0 1 .5.5v3h3a.5.5 0 0 1 0 1h-3v3a.5.5 0 0 1-1 0v-3h-3a.5.5 0 0 1 0-1h3v-3A.5.5 0 0 1 8 4z"/>
                                </svg>
                            </button>
                        </div>
                    </div>
                </div>

                <div class="d-flex w-50 justify-content-end">
                    <div class="max-md:tw-hidden tw-flex me-2">
                        <div class="mb-2 search-wrapper expanded">
                            <div class="d-flex align-items-center">
                                <form id="filter-form-search" method="get" class="w-100">
                                    <div class="d-flex mb-0 position-relative align-items-center" style="height: 26px;">
                                        <span class="svg-icon svg-icon-3 search-icon-view">
                                            <svg width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <path d="M13 13L10.1 10.1M11.6667 6.33333C11.6667 9.27885 9.27885 11.6667 6.33333 11.6667C3.38781 11.6667 1 9.27885 1 6.33333C1 3.38781 3.38781 1 6.33333 1C9.27885 1 11.6667 3.38781 11.6667 6.33333Z" stroke="#8E8C95" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                                            </svg>
                                        </span>
                                        <input id="base-search-input" type="text" name="q" class="form-control bg-white ps-12 pe-16 h-25px tw-rounded-lg" 
                                            value="{% if advance_search_filter %}{% for k, v in advance_search_filter.items %}{% if k|search_custom_field_object_items:request %}{% with channel_column=k|search_custom_field_object_items:request %}{{channel_column.name}}{% endwith %}{% else %}{% with column_display=k|display_column_items:request %}{% if k == 'tax' %}{{column_display}} (%) {% else %}{{column_display}}{% endif %}{% endwith %}{% endif %} {% translate_lang v.key|display_filter_rule:LANGUAGE_CODE LANGUAGE_CODE %} {{ v.value }}{% if not forloop.last %}, {% endif %}{% endfor %}{% elif search_q %}{{ search_q }}{% else %}{% endif %}"
                                            placeholder="{% if LANGUAGE_CODE == 'ja' %}検索{% else %}Search{% endif %}"
                                            onkeypress="if (event.keyCode == 13)document.forms['filter-form-search'].submit();"
                                        >
                                        {% if request.GET.status == 'archived' %}
                                        <input type="hidden" value="archived" name="status">
                                        {% endif %}
                                        <span class="svg-icon {% if advance_search.is_active %}svg-icon-primary{%else%}svg-icon-muted{% endif %} svg-icon-3 filter-icon filter-drawer-btn me-7"
                                            hx-get="{% url 'advance_search_drawer' %}"
                                            hx-vals='{"object_type": "{{object_type}}", "module": "{{menu_key}}", "view_id": "{{view_id}}"}'
                                            hx-indicator=".loading-drawer-spinner"
                                            hx-target="#filter-drawer-content"
                                            hx-trigger="click"
                                            hx-swap="innerHTML"
                                        >
                                            <svg width="14" height="10" viewBox="0 0 14 10" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <path d="M3 5H11M1 1H13M5 9H9" stroke="#8E8C95" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                                            </svg>
                                        </span>

                                        <span class="svg-icon svg-icon-muted svg-icon-3 filter-icon filter-drawer-btn"
                                            hx-get="{% url 'advance_search_drawer' %}"
                                            hx-vals='{"object_type": "{{object_type}}", "module": "{{menu_key}}", "search_scope":"True"}'
                                            hx-indicator=".loading-drawer-spinner"
                                            hx-target="#filter-drawer-content"
                                            hx-trigger="click"
                                            hx-swap="innerHTML"
                                        >
                                            <svg width="14" height="12" viewBox="0 0 14 12" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <path d="M1 3.33301L9 3.33301M9 3.33301C9 4.43758 9.89543 5.33301 11 5.33301C12.1046 5.33301 13 4.43758 13 3.33301C13 2.22844 12.1046 1.33301 11 1.33301C9.89543 1.33301 9 2.22844 9 3.33301ZM5 8.66634L13 8.66634M5 8.66634C5 9.77091 4.10457 10.6663 3 10.6663C1.89543 10.6663 1 9.77091 1 8.66634C1 7.56177 1.89543 6.66634 3 6.66634C4.10457 6.66634 5 7.56177 5 8.66634Z" stroke="#8E8C95" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                                            </svg>
                                        </span>
                                        
                                        <span class="svg-icon {% if advance_search.is_active %}svg-icon-primary{%else%}svg-icon-muted{% endif %} svg-icon-3 filter-icon filter-drawer-btn me-7"
                                            hx-get="{% url 'advance_search_drawer' %}"
                                            hx-vals='{"object_type": "{{object_type}}", "module": "{{menu_key}}", "view_id": "{{view_id}}"}'
                                            hx-indicator=".loading-drawer-spinner"
                                            hx-target="#filter-drawer-content"
                                            hx-trigger="click"
                                            hx-swap="innerHTML"
                                        >
                                            <svg width="14" height="10" viewBox="0 0 14 10" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <path d="M3 5H11M1 1H13M5 9H9" stroke="#8E8C95" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                                            </svg>
                                        </span>
                                        <span class="svg-icon svg-icon-muted svg-icon-3 filter-icon filter-drawer-btn"
                                            hx-get="{% url 'advance_search_drawer' %}"
                                            hx-vals='{"object_type": "{{object_type}}", "module": "{{menu_key}}", "search_scope":"True", "view_id": "{{view_id}}"}'
                                            hx-indicator=".loading-drawer-spinner"
                                            hx-target="#filter-drawer-content"
                                            hx-trigger="click"
                                            hx-swap="innerHTML"
                                        >
                                            <svg width="14" height="12" viewBox="0 0 14 12" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <path d="M1 3.33301L9 3.33301M9 3.33301C9 4.43758 9.89543 5.33301 11 5.33301C12.1046 5.33301 13 4.43758 13 3.33301C13 2.22844 12.1046 1.33301 11 1.33301C9.89543 1.33301 9 2.22844 9 3.33301ZM5 8.66634L13 8.66634M5 8.66634C5 9.77091 4.10457 10.6663 3 10.6663C1.89543 10.6663 1 9.77091 1 8.66634C1 7.56177 1.89543 6.66634 3 6.66634C4.10457 6.66634 5 7.56177 5 8.66634Z" stroke="#8E8C95" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                                            </svg>
                                        </span>

                                        <input type="hidden" value="{{view_id}}" name="view_id">
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>

                    <div class="{% include "data/utility/view-menu-search.html" %}">
                        <button 
                            onclick="openSearch()"
                            class="tw-w-[28px] tw-h-[26px] tw-justify-center tw-items-center tw-border-0 tw-bg-transparent tw-mb-2 tw-cursor-pointer hover-tooltip" type="button"
                            >
                            <span class="search-wrapper-tooltip hover-tooltip-text">
                                {% if LANGUAGE_CODE == 'ja' %}検索{% else %}Search{% endif %}
                            </span>
                            <span class="tw-flex svg-icon svg-icon-3">
                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                                    <path d="M21.7 18.9L18.6 15.8C17.9 16.9 16.9 17.9 15.8 18.6L18.9 21.7C19.3 22.1 19.9 22.1 20.3 21.7L21.7 20.3C22.1 19.9 22.1 19.3 21.7 18.9Z" fill="black" />
                                    <path opacity="0.3" d="M11 20C6 20 2 16 2 11C2 6 6 2 11 2C16 2 20 6 20 11C20 16 16 20 11 20ZM11 4C7.1 4 4 7.1 4 11C4 14.9 7.1 18 11 18C14.9 18 18 14.9 18 11C18 7.1 14.9 4 11 4ZM8 11C8 9.3 9.3 8 11 8C11.6 8 12 7.6 12 7C12 6.4 11.6 6 11 6C8.2 6 6 8.2 6 11C6 11.6 6.4 12 7 12C7.6 12 8 11.6 8 11Z" fill="black" />
                                </svg>
                            </span>
                        </button>
                    </div>
                    
                    <div class="{% include "data/utility/table-button.html" %}">
                        <button class="max-md:tw-block tw-hidden tw-w-[28px] tw-h-[26px] tw-justify-center tw-items-center tw-border-0 tw-bg-transparent tw-mb-2 tw-cursor-pointer create-view-settings-button hover-tooltip"
                            type="button"

                            {% comment %} hx-vals='{"drawer_type":"shopturbo-view-settings","page": "items", "view_id":"{{view_id}}", "download_view": true, "module": "{{menu_key}}"}'
                            hx-get="{% url 'shopturbo_load_drawer' %}" 
                            hx-target="#manage-contacts-view-settings-drawer" {% endcomment %}
                            
                            hx-get="{% url 'shopturbo_load_drawer' %}" 
                            hx-trigger="click"
                            onclick="fillItemExportIds(this)"
                            hx-target="#manage-contacts-view-settings-drawer"
                            hx-indicator=".loading-drawer-spinner,.view-form"
                            
                            >
                            <span class="search-wrapper-tooltip hover-tooltip-text">
                                {% if LANGUAGE_CODE == 'ja' %}エクスポート{% else %}Export{% endif %}
                            </span>
                            <span class="tw-flex svg-icon svg-icon-3">
                                <svg xmlns="http://www.w3.-listorg/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                                    <path d="M21 22H3C2.4 22 2 21.6 2 21C2 20.4 2.4 20 3 20H21C21.6 20 22 20.4 22 21C22 21.6 21.6 22 21 22ZM13 13.4V3C13 2.4 12.6 2 12 2C11.4 2 11 2.4 11 3V13.4H13Z" fill="black"/>
                                    <path opacity="0.3" d="M7 13.4H17L12.7 17.7C12.3 18.1 11.7 18.1 11.3 17.7L7 13.4Z" fill="black"/>
                                </svg>
                            </span>
                        </button>
                    </div>
                    <script>
                        function fillItemExportIds(elm) {
                            var itemIds = [];

                            // Read flag_all element
                            var flagAll = document.getElementById('flag_all');
                            var flagAllValue = flagAll ? (flagAll.checked || flagAll.value === 'true' || flagAll.getAttribute('data-flag-all') === 'true') : false;
                            if (flagAllValue) {
                                // Select all items
                                {% if all_shopturbo_items %}
                                var allItems = {{all_shopturbo_items|safe}};
                                allItems.forEach(function(item) {
                                    itemIds.push(item);
                                });
                                {% endif %}

                            } else {
                                // Fallback to current page checkboxes
                                var checkboxes = document.querySelectorAll('input[type="checkbox"]');
                                var checkedIds = [];
                                checkboxes.forEach(function(checkbox) {
                                    if (checkbox.checked) {
                                        checkedIds.push(checkbox.value);
                                    }
                                });
                                itemIds = checkedIds.filter(id => id !== 'on');
                            }
             
                            // Now set the hx-vals attribute with the updated account IDs
                            elm.setAttribute('hx-vals', '{"drawer_type":"shopturbo-view-sync-items","page": "items","import_export_type":"export","view_id":"{{view_filter.view.id}}", "item_ids":"' + itemIds + '", "module": "{{menu_key}}"}');
                        }
                    </script>
                    {% if permission|check_permission:'edit' %}
                    <div class="{% include "data/utility/table-button.html" %}">
                        <button id='view-sync-items' type="button" class="max-md:tw-block tw-hidden tw-w-[28px] tw-h-[26px] tw-justify-center tw-items-center tw-border-0 tw-bg-transparent tw-mb-2 tw-cursor-pointer create-view-settings-button hover-tooltip"
                            hx-get="{% url 'shopturbo_load_drawer' %}" 
                            hx-trigger="click"
                            onclick="fillItemIds(this)"
                            hx-target="#manage-contacts-view-settings-drawer"
                            hx-indicator=".loading-drawer-spinner,.view-form"
                            >
                            <span class="search-wrapper-tooltip hover-tooltip-text">
                                {% if LANGUAGE_CODE == 'ja' %}インポート{% else %}Import{% endif %}
                            </span>
                            <span class="tw-flex svg-icon svg-icon-3">
                                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M14.5 20.7259C14.6 21.2259 14.2 21.826 13.7 21.926C13.2 22.026 12.6 22.0259 12.1 22.0259C9.5 22.0259 6.9 21.0259 5 19.1259C1.4 15.5259 1.09998 9.72592 4.29998 5.82592L5.70001 7.22595C3.30001 10.3259 3.59999 14.8259 6.39999 17.7259C8.19999 19.5259 10.8 20.426 13.4 19.926C13.9 19.826 14.4 20.2259 14.5 20.7259ZM18.4 16.8259L19.8 18.2259C22.9 14.3259 22.7 8.52593 19 4.92593C16.7 2.62593 13.5 1.62594 10.3 2.12594C9.79998 2.22594 9.4 2.72595 9.5 3.22595C9.6 3.72595 10.1 4.12594 10.6 4.02594C13.1 3.62594 15.7 4.42595 17.6 6.22595C20.5 9.22595 20.7 13.7259 18.4 16.8259Z" fill="currentColor"/>
                                    <path opacity="0.3" d="M2 3.62592H7C7.6 3.62592 8 4.02592 8 4.62592V9.62589L2 3.62592ZM16 14.4259V19.4259C16 20.0259 16.4 20.4259 17 20.4259H22L16 14.4259Z" fill="currentColor"/>
                                </svg>
                            </span>
                        </button>
                        <script>
                            function fillItemIds(elm) {
                                // Call your JavaScript function to generate the account IDs dynamically
                                var checkboxes = document.querySelectorAll('input[type="checkbox"]');
                                var checkedIds = [];
                                checkboxes.forEach(function(checkbox) {
                                    if (checkbox.checked) {
                                        checkedIds.push(checkbox.value);
                                    }
                                });
                                var itemIds = checkedIds;
                                itemIds = itemIds.filter(id => id !== 'on');
                                // Now set the hx-vals attribute with the updated account IDs
                                elm.setAttribute('hx-vals', '{"drawer_type":"shopturbo-view-sync-items","page": "items","import_export_type":"import", "item_ids":"' + itemIds + '", "module": "{{menu_key}}"}');
                            }
                        </script>
                    </div>
                    {% endif %}
                </div>
            </div>

            <div id="view-header-container" class="tw-hidden w-100">
                <div class="justify-content-between align-items-center flex-row d-flex">
                    <div class="w-100">
                        {% include 'data/common/select-all-in-view-button.html' %} 
                        
                        {% if permission|check_permission:'edit' %}
                        <button class="py-1 rounded-1 btn btn-sm btn-light-primary fw-bold mt-2 mb-1" onclick="bulk_check_toggle(),check_permission_action(event, 'edit', 'edit_bulk_modal')">
                            <span>
                                {% if LANGUAGE_CODE == 'ja'%}
                                編集
                                {% else %}
                                Edit
                                {% endif %}
                            </span>
                        </button>
                        
                        <button class="py-1 rounded-1 btn btn-sm btn-light-warning fw-bold mt-2 mb-1" onclick="check_permission_action(event, 'edit')" name="bulk_duplicate" type="submit" form="order-form">
                            {% if LANGUAGE_CODE == 'ja'%}
                            複製
                            {% else %}
                            Duplicate
                            {% endif %}
                        </button>
                        {% endif %}
                        {% if permission|check_permission:'archive' %}
                        {% if request.GET.status == 'archived' %}
                        {# Show only Activate button on archived page #}
                        <button class="py-1 rounded-1 btn btn-sm btn-light-success fw-bold mt-2 mb-1 me-2" onclick="check_permission_action(event, 'archive', 'manage_restore_bulk')">
                            {% if LANGUAGE_CODE == 'ja'%}
                            有効化
                            {% else %}
                            Activate
                            {% endif %}
                        </button>
                        {# Show Delete button instead of Archive on archived page #}
                        <button class="btn btn-sm btn-light-danger py-1 rounded-1 fw-bold mt-2 mb-1 me-2" onclick="check_permission_action(event, 'archive', 'manage_permanent_delete_bulk')">
                            {% if LANGUAGE_CODE == 'ja'%}
                            削除
                            {% else %}
                            Delete
                            {% endif %}
                        </button>
                        {% else %}
                        {# Show only Archive button on normal page (hide Activate) #}
                        <button class="btn btn-sm btn-light-danger py-1 rounded-1 fw-bold mt-2 mb-1 me-2" onclick="check_permission_action(event, 'archive', 'manage_delete_bulk')">
                            {% if LANGUAGE_CODE == 'ja'%}
                            アーカイブ
                            {% else %}
                            Archive
                            {% endif %}
                        </button>
                        {% endif %}
                        {% endif %}

                        <script>
                            document.body.addEventListener("hideTaskBulkActionBtn", function(evt){
                                document.getElementById('view-header-container').classList.add('d-none')
                                document.getElementById('item-view-contianer').classList.remove('d-none')
                            })
                        </script>

                        {% if permission|check_permission:'edit' %}

                        <script>
                            function getSelectedItems() {
                                var selectedItems = [];

                                // Use cross-page selection if available
                                if (window.selectionManager && window.selectionManager.getSelectedCount() > 0) {
                                    selectedItems = window.selectionManager.getSelectedIds();
                                } else {
                                    // Fallback to current page checkboxes
                                    var classNameElements = document.getElementsByClassName("item-selection");
                                    if (classNameElements){
                                        classNameElements.forEach(function(classNameElement) {
                                            if (classNameElement.checked) {
                                                selectedItems.push(classNameElement.value);
                                            }
                                        });
                                    }
                                }

                                console.log("selected_items", selectedItems)
                                return selectedItems;
                            }
                        </script>
                        {% endif %}
                    </div>
                
                    <div class="d-flex">
                        <div class="{% include "data/utility/table-button.html" %}">
                            {% if permission|check_permission:'edit' %}
                            <button id='view-sync-items-action-drawer' type="button" class="{% include "data/utility/gray-header-button.html" %} object-action-drawer-button"
                                hx-get="{% url 'shopturbo_load_drawer' %}" 
                                hx-trigger="click"
                                onclick="fillActionItemIds(this),check_permission_action(event, 'edit')"
                                hx-target="#object-action-drawer-content"
                                hx-indicator=".loading-drawer-spinner,.view-form"
                                >
                                <span class="svg-icon svg-icon-4">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-magic" viewBox="0 0 16 16">
                                        <path d="M9.5 2.672a.5.5 0 1 0 1 0V.843a.5.5 0 0 0-1 0zm4.5.035A.5.5 0 0 0 13.293 2L12 3.293a.5.5 0 1 0 .707.707zM7.293 4A.5.5 0 1 0 8 3.293L6.707 2A.5.5 0 0 0 6 2.707zm-.621 2.5a.5.5 0 1 0 0-1H4.843a.5.5 0 1 0 0 1zm8.485 0a.5.5 0 1 0 0-1h-1.829a.5.5 0 0 0 0 1zM13.293 10A.5.5 0 1 0 14 9.293L12.707 8a.5.5 0 1 0-.707.707zM9.5 11.157a.5.5 0 0 0 1 0V9.328a.5.5 0 0 0-1 0zm1.854-5.097a.5.5 0 0 0 0-.706l-.708-.708a.5.5 0 0 0-.707 0L8.646 5.94a.5.5 0 0 0 0 .707l.708.708a.5.5 0 0 0 .707 0l1.293-1.293Zm-3 3a.5.5 0 0 0 0-.706l-.708-.708a.5.5 0 0 0-.707 0L.646 13.94a.5.5 0 0 0 0 .707l.708.708a.5.5 0 0 0 .707 0z"/>
                                    </svg>
                                </span>
                                <span class="">
                                    {% if LANGUAGE_CODE == 'ja'%}
                                    アクション
                                    {% else %}
                                    Action
                                    {% endif %}
                                </span>
                            </button>
                            {% endif %}
                            <button class="max-md:tw-block tw-hidden tw-w-[28px] tw-h-[26px] tw-justify-center tw-items-center tw-border-0 tw-bg-transparent tw-mb-2 tw-cursor-pointer create-view-settings-button hover-tooltip"
                                type="button"

                                {% comment %} hx-vals='{"drawer_type":"shopturbo-view-settings","page": "items", "view_id":"{{view_id}}", "download_view": true}'
                                hx-get="{% url 'shopturbo_load_drawer' %}" 
                                hx-target="#manage-contacts-view-settings-drawer" {% endcomment %}
                                
                                hx-get="{% url 'shopturbo_load_drawer' %}" 
                                hx-trigger="click"
                                onclick="fillActionItemIds(this),check_permission_action(event, 'edit')"
                                hx-target="#manage-contacts-view-settings-drawer"
                                hx-indicator=".loading-drawer-spinner,.view-form"
                                
                                >
                                <span class="search-wrapper-tooltip hover-tooltip-text">
                                    {% if LANGUAGE_CODE == 'ja'%}アクション{% else %}Action{% endif %}
                                </span>
                                <span class="tw-flex svg-icon svg-icon-4">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-magic" viewBox="0 0 16 16">
                                        <path d="M9.5 2.672a.5.5 0 1 0 1 0V.843a.5.5 0 0 0-1 0zm4.5.035A.5.5 0 0 0 13.293 2L12 3.293a.5.5 0 1 0 .707.707zM7.293 4A.5.5 0 1 0 8 3.293L6.707 2A.5.5 0 0 0 6 2.707zm-.621 2.5a.5.5 0 1 0 0-1H4.843a.5.5 0 1 0 0 1zm8.485 0a.5.5 0 1 0 0-1h-1.829a.5.5 0 0 0 0 1zM13.293 10A.5.5 0 1 0 14 9.293L12.707 8a.5.5 0 1 0-.707.707zM9.5 11.157a.5.5 0 0 0 1 0V9.328a.5.5 0 0 0-1 0zm1.854-5.097a.5.5 0 0 0 0-.706l-.708-.708a.5.5 0 0 0-.707 0L8.646 5.94a.5.5 0 0 0 0 .707l.708.708a.5.5 0 0 0 .707 0l1.293-1.293Zm-3 3a.5.5 0 0 0 0-.706l-.708-.708a.5.5 0 0 0-.707 0L.646 13.94a.5.5 0 0 0 0 .707l.708.708a.5.5 0 0 0 .707 0z"/>
                                    </svg>
                                </span>
                            </button>
                        </div>
                        <div class="{% include "data/utility/table-button.html" %} tw-mr-2">
                            <button class="{% include "data/utility/gray-header-button.html" %} create-view-settings-button" type="button"
                                hx-get="{% url 'shopturbo_load_drawer' %}" 
                                hx-trigger="click"
                                onclick="fillItemExportIds(this)"
                                hx-target="#manage-contacts-view-settings-drawer"
                                hx-indicator=".loading-drawer-spinner,.view-form"
                                >
                                <span class="svg-icon svg-icon-4">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-upload" viewBox="0 0 16 16">
                                        <path d="M.5 9.9a.5.5 0 0 1 .5.5v2.5a1 1 0 0 0 1 1h12a1 1 0 0 0 1-1v-2.5a.5.5 0 0 1 1 0v2.5a2 2 0 0 1-2 2H2a2 2 0 0 1-2-2v-2.5a.5.5 0 0 1 .5-.5"/>
                                        <path d="M7.646 1.146a.5.5 0 0 1 .708 0l3 3a.5.5 0 0 1-.708.708L8.5 2.707V11.5a.5.5 0 0 1-1 0V2.707L5.354 4.854a.5.5 0 1 1-.708-.708z"/>
                                    </svg>
                                </span>
                                <span class="">
                                    {% if LANGUAGE_CODE == 'ja'%}
                                    エクスポート
                                    {% else %}
                                    Export
                                    {% endif %}
                                </span>
                            </button>
                            <button class="max-md:tw-block tw-hidden tw-w-[28px] tw-h-[26px] tw-justify-center tw-items-center tw-border-0 tw-bg-transparent tw-mb-2 tw-cursor-pointer create-view-settings-button hover-tooltip"
                                type="button"

                                {% comment %} hx-vals='{"drawer_type":"shopturbo-view-settings","page": "items", "view_id":"{{view_id}}", "download_view": true}'
                                hx-get="{% url 'shopturbo_load_drawer' %}" 
                                hx-target="#manage-contacts-view-settings-drawer" {% endcomment %}
                                
                                hx-get="{% url 'shopturbo_load_drawer' %}" 
                                hx-trigger="click"
                                onclick="fillItemExportIds(this)"
                                hx-target="#manage-contacts-view-settings-drawer"
                                hx-indicator=".loading-drawer-spinner,.view-form"
                                
                                >
                                <span class="search-wrapper-tooltip hover-tooltip-text">
                                    {% if LANGUAGE_CODE == 'ja' %}エクスポート{% else %}Export{% endif %}
                                </span>
                                <span class="tw-flex svg-icon svg-icon-3">
                                    <svg xmlns="http://www.w3.-listorg/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                                        <path d="M21 22H3C2.4 22 2 21.6 2 21C2 20.4 2.4 20 3 20H21C21.6 20 22 20.4 22 21C22 21.6 21.6 22 21 22ZM13 13.4V3C13 2.4 12.6 2 12 2C11.4 2 11 2.4 11 3V13.4H13Z" fill="black"/>
                                        <path opacity="0.3" d="M7 13.4H17L12.7 17.7C12.3 18.1 11.7 18.1 11.3 17.7L7 13.4Z" fill="black"/>
                                    </svg>
                                </span>
                            </button>
                        </div>
                        <script>
                            function fillItemExportIds(elm) {
                                var itemIds = [];

                                // Read flag_all element
                                var flagAll = document.getElementById('flag_all');
                                var flagAllValue = flagAll ? (flagAll.checked || flagAll.value === 'true' || flagAll.getAttribute('data-flag-all') === 'true') : false;
                                if (flagAllValue) {
                                    // Select all items
                                    {% if all_shopturbo_items %}
                                    var allItems = {{all_shopturbo_items|safe}};
                                    allItems.forEach(function(item) {
                                        itemIds.push(item);
                                    });
                                    {% endif %}
                                    
                                } else {

                                    // Fallback to current page checkboxes
                                    var checkboxes = document.querySelectorAll('input[type="checkbox"]');
                                    var checkedIds = [];
                                    checkboxes.forEach(function(checkbox) {
                                        if (checkbox.checked) {
                                            checkedIds.push(checkbox.value);
                                        }
                                    });
                                    itemIds = checkedIds.filter(id => id !== 'on');
                                }
              
                                // Now set the hx-vals attribute with the updated account IDs
                                elm.setAttribute('hx-vals', '{"drawer_type":"shopturbo-view-sync-items","page": "items","import_export_type":"export","view_id":"{{view_filter.view.id}}", "item_ids":"' + itemIds + '", "module": "{{menu_key}}"}');
                            }
                        </script>
                    </div>
                </div>
                {% include 'data/common/select-all-in-view-record-msg.html' %} 
            </div>

        </div>
    </div>
    {% comment %} End of Views {% endcomment %}

    <div class="{% if search_q %}max-md:tw-flex {% else %}max-md:tw-hidden {% endif %}tw-hidden tw-items-center tw-py-2 border-bottom border-bottom-1 tw-transition-all tw-duration-300" id="search-bar">
        <form id="filter-form-search" method="get" class="w-100">
            <div class="d-flex mb-0 position-relative align-items-center" style="height: 26px;">
                <span class="svg-icon svg-icon-3 search-icon-view">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                        <path d="M21.7 18.9L18.6 15.8C17.9 16.9 16.9 17.9 15.8 18.6L18.9 21.7C19.3 22.1 19.9 22.1 20.3 21.7L21.7 20.3C22.1 19.9 22.1 19.3 21.7 18.9Z" fill="black" />
                        <path opacity="0.3" d="M11 20C6 20 2 16 2 11C2 6 6 2 11 2C16 2 20 6 20 11C20 16 16 20 11 20ZM11 4C7.1 4 4 7.1 4 11C4 14.9 7.1 18 11 18C14.9 18 18 14.9 18 11C18 7.1 14.9 4 11 4ZM8 11C8 9.3 9.3 8 11 8C11.6 8 12 7.6 12 7C12 6.4 11.6 6 11 6C8.2 6 6 8.2 6 11C6 11.6 6.4 12 7 12C7.6 12 8 11.6 8 11Z" fill="black" />
                    </svg>
                </span>
                <input id="base-search-input" type="text" name="q" class="form-control bg-white pe-12 ps-12"
                value="{% if advance_search_filter %}{% for k, v in advance_search_filter.items %}{% if k|search_custom_field_object_items:request %}{% with channel_column=k|search_custom_field_object_items:request %}{{channel_column.name}}{% endwith %}{% else %}{% with column_display=k|display_column_items:request %}{% if k == 'tax' %}{{column_display}} (%) {% else %}{{column_display}}{% endif %}{% endwith %}{% endif %} {% translate_lang v.key|display_filter_rule:LANGUAGE_CODE LANGUAGE_CODE %} {{ v.value }}{% if not forloop.last %}, {% endif %}{% endfor %}{% elif search_q %}{{ search_q }}{% else %}{% endif %}"                       
                placeholder="{% if LANGUAGE_CODE == 'ja' %}検索{% else %}Search{% endif %}"
                onkeypress="if (event.keyCode == 13)document.forms['filter-form-search'].submit();"
                >
                {% if request.GET.status == 'archived' %}
                <input type="hidden" value="archived" name="status">
                {% endif %}
                
                <span class="svg-icon {% if advance_search.is_active %}svg-icon-primary{%else%}svg-icon-muted{% endif %} svg-icon-3 filter-icon filter-drawer-btn"
                    hx-get="{% url 'advance_search_drawer' %}"
                    {% if custom_object %}
                        hx-vals='{"object_type": "{{constant.TYPE_OBJECT_CUSTOM_OBJECT}}", "custom_object_id": "{{custom_object.id}}", "module": "{{menu_key}}"}'
                    {% else %}
                        hx-vals='{"object_type": "{{constant.TYPE_OBJECT_CUSTOM_OBJECT}}", "module": "{{menu_key}}"}'
                    {% endif %}
                    hx-indicator=".loading-drawer-spinner"
                    hx-target="#filter-drawer-content"
                    hx-trigger="click"
                    hx-swap="innerHTML"
                >
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M21 7H3C2.4 7 2 6.6 2 6V4C2 3.4 2.4 3 3 3H21C21.6 3 22 3.4 22 4V6C22 6.6 21.6 7 21 7Z" fill="currentColor"/>
                        <path opacity="0.3" d="M21 14H3C2.4 14 2 13.6 2 13V11C2 10.4 2.4 10 3 10H21C21.6 10 22 10.4 22 11V13C22 13.6 21.6 14 21 14ZM22 20V18C22 17.4 21.6 17 21 17H3C2.4 17 2 17.4 2 18V20C2 20.6 2.4 21 3 21H21C21.6 21 22 20.6 22 20Z" fill="currentColor"/>
                    </svg>
                </span>
                <input type="hidden" value="{{view_id}}" name="view_id">
            </div>
        </form>
    </div>

    <div class="mb-2 d-none" id="d-select-additional-options">
        <div class="status bg-light-dark text-center pt-4 pb-3 px-5 rounded">
            
            {% if LANGUAGE_CODE == 'ja'%}
            このページの全てのレコードが選択されました。 
            {% else %}
            All records on this page are selected. 
            {% endif %}

            <a onclick="toggleText()" 
                class="btn btn-dark" 
                data-bs-toggle="collapse" 
                id="select-additional-options-toggle" 
                role="button" 
                aria-expanded="false" 
                aria-controls="collapseExample">
                {% if LANGUAGE_CODE == 'ja'%}
                すべてのレコードを選択する
                {% else %}
                Select all records
                {% endif %}

                </a>
        </div>
    </div>

    <div class="d-flex flex-column flex-lg-row">
        <div class="flex-lg-row-fluid">
            <form method="POST" id="order-form" data-bulk-action="true">
                {% csrf_token %}        
                <div class="pt-0 table-responsive" style="max-height: 75vh;">
                    <table class="{% include "data/utility/table.html" %} items-table">
                        <thead class="{% include "data/utility/table-header.html" %}">
                            <tr class="align-middle">
                                {% include "data/shopturbo/items/shopturbo-item-table-header.html" %}
                            </tr>
                        </thead>
                        <tbody class="fs-6">
                            {% comment %} Normal Table {% endcomment %}
                            {% if view_filter.view_type == 'list' %}
                                {% for item in shopturbo_items %}
                                    <tr id="row-{{item.id}}" 
                                    hx-get="{% host_url "shopturbo_item_row_detail" item.id host 'app' %}" hx-trigger="load"
                                    hx-vals='js:{ "menu_key": "{{menu_key}}", "view_id": "{{view_filter.view.id}}"}'
                                    >
                                        <td class="d-flex justify-content-center w-100">
                                            <style>
                                                /* Styles for the spinner */
                                                .row_load-{{item.id}} {
                                                    display: none; /* Initially hidden */
                                                }
                                                .htmx-request .row_load-{{item.id}},
                                                .htmx-request.row_load-{{item.id}} {
                                                    display: inline-block; /* Display during htmx request */
                                                }
                                            </style>
                                            <!-- Spinner icon -->
                                            <span class="spinner-border spinner-border-lg text-secondary row_load-{{item.id}}" role="status">
                                                <span class="visually-hidden">Loading...</span>
                                            </span>
                                        </td>
                                    </tr>
                                {% endfor %}
                            {% comment %} Grouped Table {% endcomment %}
                            {% elif view_filter.view_type == 'group' %}
                                {% comment %} Parent of The Group {% endcomment %}
                                {% for item in shopturbo_items.parent_items %}
                                    <tr id="row-{{item.id}}">
                                        {% include 'data/shopturbo/items-row-partial.html' %}
                                    </tr>
                                {% endfor %}
        
                                <tr id="row-others-group">
                                    {% for shopturbo_items_column in shopturbo_items_columns %}
                                        {% if shopturbo_items_column == 'dropdown' %}
                                            <td class="w-40px" onclick="toggleChild(this, 'others')">
                                                <div class="tw-ml-5"></div>
                                            </td>
                                        {% else %}
                                            <td>
                                                {% if shopturbo_items_column == 'checkbox' %} 
                                                    <span class="fw-bolder">
                                                        {% if LANGUAGE_CODE == 'ja'%}
                                                        その他
                                                        {% else %}
                                                        Others
                                                        {% endif %}
                                                    </span>
                                                {% endif %}
                                            </td>
                                        {% endif %}
                                        {% if shopturbo_items_column == 'item_id' %}
                                            <td>
                                            </td>
                                        {% endif %}
                                    {% endfor %}
                                </tr>
        
                                {% comment %} Child of The Group {% endcomment %}
                                {% for item in shopturbo_items.other_items.items %}
                                    <tr id="row-{{item.id}}-" class="table-active child-others">
                                        {% include 'data/shopturbo/items-row-partial.html' with item=item child_id=item.id is_others='true' menu_key=menu_key %}
                                    </tr>
                                {% endfor %}
        
                                {% if shopturbo_items.other_items.is_more %}
                                    <tr id="row-more-" class="table-active child-others">
                                        {% for shopturbo_items_column in shopturbo_items_columns %}
                                        <td>
                                            {% if shopturbo_items_column == 'item_id' %}
                                                <div class="align-items-center d-flex btn btn-dark-outline btn-md py-1 text-dark text-hover-primary fw-bolder px-1"
                                                    hx-get="{% host_url 'shopturbo_item_group_row' host 'app' %}" 
                                                    hx-vals='js:{"current_length": getCurrentChildParent("", "1"), "module": "{{menu_key}}", "parent_group_id": "others", "view_id": "{{view_filter.view.id}}"}'
                                                    hx-trigger="click" 
                                                    hx-indicator=".load-child-row-"
                                                    hx-target="#row-more-"
                                                >
                                                    <span>
                                                        {% if LANGUAGE_CODE == 'ja'%}
                                                        さらに表示
                                                        {% else %}
                                                        Load More
                                                        {% endif %}
                                                    </span>
                                                    <style>
                                                        /* Styles for the spinner */
                                                        .load-child-row- {
                                                            display: none; /* Initially hidden */
                                                        }
                                                        .htmx-request .load-child-row-,
                                                        .htmx-request.load-child-row- {
                                                            display: inline-block; /* Display during htmx request */
                                                        }
                                                    </style>
                                                    <!-- Spinner icon -->
                                                    <span class="spinner-border spinner-border-lg text-secondary load-child-row- ms-2" role="status">
                                                        <span class="visually-hidden">Loading...</span>
                                                    </span>
                                                </div>
                                            {% endif %}
                                        </td>
                                        {% if shopturbo_items_column == 'item_id' %}
                                            <td>
                                            </td>
                                        {% endif %}
                                        {% endfor %}
                                    </tr>
                                {% endif %}
        
                            {% elif view_filter.view_type == 'price_table' %}
                                {% include 'data/shopturbo/items-row-price-table.html' %}
                            {% endif %}
                        </tbody> 
                    </table>
        
                    <input name='flag_all' id='flag_all' class="flag_all flag_all_item" hidden type="checkbox" ></input>
                    {% if view_id %}
                    <input type="hidden" value="{{view_id}}" name="view_id">
                    {% endif %}
                    
                    <div class="modal fade" tabindex="-1" id="manage_delete_bulk">
                        <div class="modal-dialog">
                            <div class="modal-content">
                                <div class="modal-header pb-0 border-0 justify-content-end">
                                    <div class="btn btn-icon btn-sm btn-active-light-primary ms-2" data-bs-dismiss="modal" aria-label="Close">
                                    </div>
                                </div>
                                <div class="modal-body pb-0">
                                    <div class="mb-13 text-center">
                                        <h3 class="modal-title">
                                            
                                            {% if LANGUAGE_CODE == 'ja'%}
                                            一括アーカイブの確認
                                            {% else %}
                                            Bulk Archive Confirmation
                                            {% endif %}
                                        
                                        </h3>
                                    </div>
                                    <div class="border-bottom">
                                        <div class="fv-rowd-flex flex-column mb-8">
                                            <label class="{% include 'data/utility/form-label.html' %}">
                                                <span class="">
                                                    {% if LANGUAGE_CODE == 'ja'%}
                                                    選択されたレコードをアーカイブしてもよろしいですか?
                                                    {% else %}
                                                    Are you sure to archive selected records?
                                                    {% endif %}
                                                </span>
                                            </label>
                                    
                                        </div>
                                    </div>
                                </div>
                                
                
                                <div class="modal-footer border-0">
                                    <button name="bulk_delete_items" type="submit" class="btn btn-danger">
                                        
                                        {% if LANGUAGE_CODE == 'ja'%}
                                            アーカイブ
                                            {% else %}
                                            Archive
                                            {% endif %}
                                        </button>
                                    
                                    </button>
                                    <a data-bs-dismiss="modal" class="btn bg-gray-200 border">
                                        {% if LANGUAGE_CODE == 'ja'%}
                                        キャンセル
                                        {% else %}
                                        Cancel
                                        {% endif %}
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div> 
        
                    <div class="modal fade" tabindex="-1" id="manage_restore_bulk">
                        <div class="modal-dialog">
                            <div class="modal-content">
                                <div class="modal-header pb-0 border-0 justify-content-end">
                                    <div class="btn btn-icon btn-sm btn-active-light-primary ms-2" data-bs-dismiss="modal" aria-label="Close">
                                    </div>
                                </div>
                                <div class="modal-body pb-0">
                                    <div class="mb-13 text-center">
                                        <h3 class="modal-title">
                                            
                                            {% if LANGUAGE_CODE == 'ja'%}
                                            一括有効化の確認
                                            {% else %}
                                            Bulk Activation Confirmations
                                            {% endif %}
                                        
                                        </h3>
                                    </div>
                                    <div class="border-bottom">
                                        <div class="fv-rowd-flex flex-column mb-8">
                                            <label class="{% include 'data/utility/form-label.html' %}">
                                                <span class="">
                                                    {% if LANGUAGE_CODE == 'ja'%}
                                                    これらの商品を有効化してもよろしいですか?
                                                    {% else %}
                                                    Are you sure to activate these items?
                                                    {% endif %}
                                                </span>
                                            </label>
                                       
                                        </div>
                                    </div>
                                </div>
                                
                
                                <div class="modal-footer border-0">
                                    <button name="bulk_restore_items" type="submit" class="btn btn-success">
                                        {% if LANGUAGE_CODE == 'ja'%}
                                        有効化
                                        {% else %}
                                        Activate
                                        {% endif %}
                                    </button>
                                    <a data-bs-dismiss="modal" class="btn bg-gray-200 border">
                                        {% if LANGUAGE_CODE == 'ja'%}
                                        キャンセル
                                        {% else %}
                                        Cancel
                                        {% endif %}
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
        
                    <div class="modal fade" tabindex="-1" id="manage_permanent_delete_bulk">
                        <div class="modal-dialog">
                            <div class="modal-content">
                                <div class="modal-header pb-0 border-0 justify-content-end">
                                    <div class="btn btn-icon btn-sm btn-active-light-primary ms-2" data-bs-dismiss="modal" aria-label="Close">
                                    </div>
                                </div>
                                <div class="modal-body pb-0">
                                    <div class="mb-13 text-center">
                                        <h3 class="modal-title">
        
                                            {% if LANGUAGE_CODE == 'ja'%}
                                            一括削除の確認
                                            {% else %}
                                            Bulk Delete Confirmation
                                            {% endif %}
        
                                        </h3>
                                    </div>
                                    <div class="border-bottom">
                                        <div class="fv-rowd-flex flex-column mb-8">
                                            <label class="{% include 'data/utility/form-label.html' %}">
                                                <span class="">
                                                    {% if LANGUAGE_CODE == 'ja'%}
                                                    選択されたレコードを完全に削除してもよろしいですか？この操作は元に戻すことができません。
                                                    {% else %}
                                                    Are you sure to permanently delete selected records? This action cannot be undone.
                                                    {% endif %}
                                                </span>
                                            </label>
        
                                        </div>
                                    </div>
                                </div>
        
        
                                <div class="modal-footer border-0">
                                    <button name="bulk_permanent_delete_items" type="submit" class="btn btn-danger">
        
                                        {% if LANGUAGE_CODE == 'ja'%}
                                            削除
                                            {% else %}
                                            Delete
                                            {% endif %}
                                        </button>
                                    <a data-bs-dismiss="modal" class="btn bg-gray-200 border">
                                        {% if LANGUAGE_CODE == 'ja'%}
                                        キャンセル
                                        {% else %}
                                        Cancel
                                        {% endif %}
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
        
                </div>
            </form>
        </div>
    </div>
    
    {% if view_filter.view_type == 'list' and shopturbo_items|length > 0 %}
        <div class="{% include "data/utility/pagination.html" %}">
            {% if LANGUAGE_CODE == 'ja'%}
                {{paginator_item_begin}}–{{paginator_item_end}} の {{paginator.count}} 件
            {% else %}
                Viewing {{paginator_item_begin}}–{{paginator_item_end}} of {{paginator.count}} results
            {% endif %}
            
            <div>
                {% if page_content.has_previous %}     
                    <a class="btn tw-bg-gray-200 hover:tw-bg-gray-300  w-100px" href="{% host_url 'load_object_page' menu_key object_type|object_type_slug:request host 'app' %}?page=1&{% query_transform %}">&laquo; {% translate_lang "First" LANGUAGE_CODE %}</a>
                    <a class="btn tw-bg-gray-200 hover:tw-bg-gray-300  w-100px" href="{% host_url 'load_object_page' menu_key object_type|object_type_slug:request host 'app' %}?page={{ page_content.previous_page_number }}&{% query_transform %}">&lsaquo; {% translate_lang "Previous" LANGUAGE_CODE %}</a>
                {% endif %}
                        
                {% if page_content.has_next %}
                    <a class="btn tw-bg-gray-200 hover:tw-bg-gray-300  w-100px" href="{% host_url 'load_object_page' menu_key object_type|object_type_slug:request host 'app' %}?page={{ page_content.next_page_number }}&{% query_transform %}">{% translate_lang "Next" LANGUAGE_CODE %} &rsaquo;</a>
                    <a class="btn tw-bg-gray-200 hover:tw-bg-gray-300  w-100px" href="{% host_url 'load_object_page' menu_key object_type|object_type_slug:request host 'app' %}?page={{ page_content.paginator.num_pages }}&{% query_transform %}">{% translate_lang "Last" LANGUAGE_CODE %} &raquo;</a>
                {% endif %}
            </div>
        </div>
    {% else %}
        <div class="mb-10">
        </div>
    {% endif %}

    

</div>

{% if item_id %}
<a id="selected_show_items" class="d-none manage-full-wizard-button" 
    hx-get="{% url 'shopturbo_load_drawer' %}" 
    hx-vals = '{"drawer_type":"item-manage", "item_id":"{{item_id}}", "view_id":"{{view_id}}", "module": "{{menu_key}}", "side_drawer": "{{side_drawer}}" }'
    hx-target="#manage-full-drawer-content"
    hx-indicator=".loading-drawer-spinner"  
    hx-trigger="click"
>
<script>
    $(document).ready(function() {
        setTimeout(function() {
            document.getElementById('selected_show_items').click();
        }, 0);
    });
</script>

{% endif %}


<script>
    // Function to load script dynamically
    function loadScript(url, callback) {
        var script = document.createElement('script');
        script.type = 'text/javascript';
        script.src = url;
        script.onload = callback;
        script.onerror = function() {
            console.error('Failed to load script:', url);
        };
        document.head.appendChild(script);
    }

    // Check if jQuery is loaded, if not, load it
    function ensureJQuery(callback) {
        if (typeof window.jQuery === 'undefined') {
            console.log('jQuery not loaded, loading now...');
            loadScript('https://cdn.jsdelivr.net/jquery/latest/jquery.min.js', function() {
                console.log('jQuery loaded successfully');
                if (callback) callback();
            });
        } else {
            console.log('jQuery already loaded');
            if (callback) callback();
        }
    }

    // Check if DataTable is loaded, if not, load it
    function ensureDataTableScripts(callback) {
        if (typeof $.fn.DataTable === 'undefined') {
            console.log('DataTable not loaded, loading scripts...');
            loadScript('https://cdn.datatables.net/1.13.7/js/jquery.dataTables.min.js', function() {
                console.log('DataTable core loaded');
                loadScript('https://cdn.datatables.net/fixedcolumns/4.3.0/js/dataTables.fixedColumns.min.js', function() {
                    console.log('DataTable FixedColumns loaded');
                    if (callback) callback();
                });
            });
        } else {
            console.log('DataTable already loaded');
            if (callback) callback();
        }
    }

    var table = null;
    var requestNum = {% if shopturbo_items|length == 0 %}1{% else %}{{shopturbo_items|length}}{% endif %}
    
    {% if view_type != 'price_table' %}
    // Make sure jQuery and DataTables are properly initialized
    function ensureDataTable(callback) {
        // First ensure jQuery is loaded
        ensureJQuery(function() {
            // Then ensure DataTable scripts are loaded
            ensureDataTableScripts(function() {
                if (callback && typeof callback === 'function') {
                    callback();
                }
            });
        });
    }
    {% endif %}
    
    {% if shopturbo_items|length == 0 %}
        $(document).ready(function(){
            ensureDataTable(function() {
                table = $(".items-table").DataTable({
                    responsive: true,
                    scrollX:        true,
                    scrollY:        "75vh",
                    scrollCollapse: true,
                    fixedColumns:   {
                        left: 3
                    },
                    fixedHeader:   true,
                    ordering: false,
                    searching: false,  // Hide the search bar
                    paging: false,      // Hide pagination
                    info: false,        // Hide the information text
                    language: {
                        emptyTable: "{% translate_lang 'No data available in table' LANGUAGE_CODE %}"
                    }
                });
            });
        });
    {% else %}
        document.addEventListener('htmx:afterRequest', function(evt) {
            if (requestNum <= 0) return;

            if (evt.target.tagName.toLowerCase() === 'tr') {
                requestNum -= 1;
                if (requestNum <= 0) {
                    // Ensure DataTable is ready before initializing
                    ensureDataTable(function() {
                        table = $(".items-table").DataTable({
                            responsive: true,
                            scrollX:        true,
                            scrollY:        "75vh",
                            scrollCollapse: true,
                            fixedColumns:   {
                                left: 3
                            },
                            fixedHeader:   true,
                            ordering: false,
                            searching: false,  // Hide the search bar
                            paging: false,      // Hide pagination
                            info: false,        // Hide the information text
                            language: {
                                emptyTable: "{% translate_lang 'No data available in table' LANGUAGE_CODE %}"
                            }
                        });

                        const dropdowns = $('.dropdown-toggle', table.cells(null, {% if view_filter.view_type == 'list' %}1{% else %}2{% endif %}).nodes());
                        const dropdown = dropdowns.each((index, dropdownToggleEl) => {
                            var instance = new bootstrap.Dropdown(dropdownToggleEl, {
                                popperConfig(defaultBsPopperConfig) {
                                    return { ...defaultBsPopperConfig, strategy: "fixed" };
                                },
                            });

                            dropdownToggleEl.addEventListener("show.bs.dropdown", function (event) {
                                $(event.target).closest("td").addClass("z-index-3");
                            });

                            dropdownToggleEl.addEventListener("hide.bs.dropdown", function (event) {
                                $(event.target).closest("td").removeClass("z-index-3");
                            });
                        });
                        {% include "data/common/open-drawer.js" %}
                    });
                }
            }
        });
    {% endif %}

    function refresh_table() {
        if (table != null) {
            try {
                table.columns.adjust();
            } catch (e) {
                console.log("Error adjusting columns:", e);
            }
        }
    }

    function getHTMLFromFragment(fragment) {
        const tempDiv = document.createElement('div');
        tempDiv.appendChild(fragment.cloneNode(true)); // Clone to avoid removing nodes
        return tempDiv.innerHTML;
    }

    function getAllNextSiblings(element) {
        const siblings = [];
        let nextSibling = element.nextElementSibling;
    
        while (nextSibling) {
            siblings.push(nextSibling);
            nextSibling = nextSibling.nextElementSibling;
        }
    
        return siblings;
    }

    document.addEventListener('htmx:afterRequest', function(evt) {
        refresh_table()

        if (typeof evt.detail.xhr.responseURL === 'string' && evt.detail.xhr.responseURL.includes('/items/row/group/')) {
            var responseText = evt.detail.xhr.responseText;
            var tempDiv = document.createElement('div');
            tempDiv.innerHTML = responseText;
            var targetElement = evt.detail.target;

            if (targetElement.id.includes('row-more-')) {
                targetElement = targetElement.previousElementSibling;
                //Delete target element
                evt.detail.target.remove();
            }
            
            try {
                var node = tempDiv.firstElementChild.content
                var content = getHTMLFromFragment(node);
                targetElement.insertAdjacentHTML('afterend', content);

                var allNextSiblings = getAllNextSiblings(targetElement);
                allNextSiblings.forEach(sibling => htmx.process(sibling));
            } catch (e) {
                return;
            }

            if (evt.detail.elt.id.includes('item-dropdown')) {
                evt.detail.elt.setAttribute('hx-disable', null);
                htmx.process(evt.detail.elt);
            }
        }

        var dropdowns = document.getElementsByClassName('dropdown-toggle')
        var dropdown = dropdowns.forEach(function(dropdownToggleEl) {
            var instance = new bootstrap.Dropdown(dropdownToggleEl, {
                popperConfig(defaultBsPopperConfig) {
                    return { ...defaultBsPopperConfig, strategy: "fixed" };
                },
            });

            dropdownToggleEl.addEventListener("show.bs.dropdown", function (event) {
                $(event.target).closest("td").addClass("z-index-3");
            });

            dropdownToggleEl.addEventListener("hide.bs.dropdown", function (event) {
                $(event.target).closest("td").removeClass("z-index-3");
            });

        });

        refresh_table();
    })

    function toggleChild(elm, id, parent=null){
        elm.querySelector('svg').classList.toggle('open');

        if (id == "others"){
            var childs = document.getElementsByClassName("child-" + id)
        } else {
            var childs_level_1 = [...document.getElementsByClassName("child-1-" + id)];
            var childs_level_2 = [...document.getElementsByClassName("child-2-" + id)];
            var childs = [...childs_level_1, ...childs_level_2];
        }

        var dropdown_childs = document.getElementsByClassName("dropdown-" + id);
        dropdown_childs.forEach(function(dropdown_child) {
            dropdown_child.querySelector('svg').classList.toggle('open');
        })


        childs.forEach(function(child) {
            if (parent){
                if (child.classList.contains("child-2-" + parent)){
                    if (elm.querySelector('svg').classList.contains('open')) {
                        child.style.display = "none";
                    } else {
                        child.style.display = "";
                    }
                }
            } else {
                if (elm.querySelector('svg').classList.contains('open')) {
                    child.style.display = "none";
                } else {
                    child.style.display = "";
                }
            }
        })

        refresh_table()
    }

    function getCurrentChildParent(parent_id, level_type, main_parent_id=null) {
        if (parent_id === "") {
            parent_id = "others";
        }
        
        let currentChildParent;
        if (parent_id === "others") {
            currentChildParent = document.getElementsByClassName("child-" + parent_id);
        } else {
            currentChildParent = document.getElementsByClassName("child-" + level_type + "-" + parent_id);
            if (main_parent_id) {
                // Convert HTMLCollection to Array for easier manipulation
                currentChildParent = Array.from(currentChildParent).filter(element => 
                    element.classList.contains("child-" + level_type + "-" + main_parent_id)
                );
            }
        }
        return currentChildParent.length;
    }

    function toggleText() {
        console.log('toggleText() called');
        var x = document.getElementById("select-additional-options-toggle");
        var toggle_data = x.getAttribute('toggle-data')
        console.log('Current toggle_data:', toggle_data);

        if (toggle_data !== "true") {
            // First click: Change to "Clear All" mode and select all items
            console.log('toggleText() - Switching to Clear All mode');

            {% if LANGUAGE_CODE == 'ja'%}
            x.innerHTML = "選択を解除";
            {% else %}
            x.innerHTML = "Clear All";
            {% endif %}

            $(".flag_all").each(function(index, element) {
                element.value = true
            });

            x.setAttribute('toggle-data',"true")

        } else {
            // Second click: Actually clear all selections
            console.log('toggleText() - Clearing all selections');

            x.setAttribute('toggle-data',"false")

            addcontactelem = document.getElementById("update-items");
            addcontactelem.classList.add("disabled");

            // Use SelectionManager if available, otherwise fallback to direct manipulation
            console.log('toggleText() - Clear All section called');
            if (window.selectionManager) {
                console.log('toggleText() - Using SelectionManager.clearAllSelections()');
                window.selectionManager.clearAllSelections();
            } else {
                console.log('toggleText() - Using fallback checkbox clearing');
                $('input[type=checkbox]').prop('checked', false);
            }

            //Hide
            var element_select_options = document.getElementById("d-select-additional-options");
            element_select_options.classList.add("d-none")

            x.innerHTML = "{% if LANGUAGE_CODE == 'ja'%}すべて選択 ({{paginator.count}}) このセクションの連絡先{%else%}Select All ({{paginator.count}}) contacts in this sections{%endif%}";

            $(".flag_all").each(function(index, element) {
                element.value = false
            });

        }
      }



    function select_all() {
        $('#selectAll').prop('checked', !$('#selectAll').prop('checked'));
        if ($('#selectAll').prop('checked') == true) {
            addcontactelem = document.getElementById("update-items");
            addcontactelem.classList.add("disabled");

            // Use SelectionManager if available, otherwise fallback to direct manipulation
            if (window.selectionManager) {
                window.selectionManager.clearAllSelections();
            } else {
                $('input[type=checkbox]').prop('checked', false);
            }

            var element_select_options = document.getElementById("d-select-additional-options");
            element_select_options.classList.add("d-none")

        } else {
            addcontactelem = document.getElementById("update-items");
            addcontactelem.classList.remove("disabled");

            // Use SelectionManager if available, otherwise fallback to direct manipulation
            if (window.selectionManager) {
                window.selectionManager.selectAllOnPage();
            } else {
                $('input[type=checkbox]').prop('checked', true);
            }

            var element_select_options = document.getElementById("d-select-additional-options");
            element_select_options.classList.remove("d-none")

        }
    }
</script>

<script>
    // Cross-page selection management
    class SelectionManager {
        constructor() {
            this.selectedIds = new Set();
            this.maxUrlIds = 100; // Prevent URL length issues
            this.initFromURL();
            this.bindEvents();
            this.updateUI();
        }

        initFromURL() {
            console.log('initFromURL() called');
            console.log('Current URL:', window.location.href);
            console.log('URL search params:', window.location.search);

            const urlParams = new URLSearchParams(window.location.search);
            console.log('All URL parameters:', Object.fromEntries(urlParams));

            const selectedIds = urlParams.get('selected_ids');
            console.log('URL selected_ids parameter:', selectedIds);

            if (selectedIds) {
                this.selectedIds = new Set(selectedIds.split(',').filter(id => id.trim()));
                console.log('Parsed selected IDs from URL:', Array.from(this.selectedIds));
            } else {
                console.log('No selected_ids parameter found in URL');
            }

            // Also initialize from currently checked checkboxes (server-side rendered state)
            this.initFromCurrentCheckboxes();

            // Now update checkboxes to ensure consistency
            this.updateCheckboxes();
        }

        initFromCurrentCheckboxes() {
            console.log('initFromCurrentCheckboxes() called');
            // Get currently checked checkboxes and add them to selectedIds
            const checkboxes = document.querySelectorAll('.item-selection');
            console.log('Found checkboxes for init:', checkboxes.length);

            let checkedCount = 0;
            checkboxes.forEach((checkbox, index) => {
                console.log(`Checkbox ${index} (${checkbox.value}): checked = ${checkbox.checked}`);
                if (checkbox.checked && checkbox.value && checkbox.value !== 'on') {
                    this.selectedIds.add(String(checkbox.value));
                    checkedCount++;
                    console.log(`Added ${checkbox.value} to selectedIds from checked checkbox`);
                }
            });

            console.log(`initFromCurrentCheckboxes() completed. Found ${checkedCount} checked checkboxes.`);
        }

        toggleSelection(itemId, updateURL = true) {
            const itemIdStr = String(itemId);
            if (this.selectedIds.has(itemIdStr)) {
                this.selectedIds.delete(itemIdStr);
            } else {
                this.selectedIds.add(itemIdStr);
            }

            if (updateURL) {
                this.updateURL();
            }
            this.updateUI();
            this.updateBulkActionButtons();
        }

        addSelection(itemId, updateURL = true) {
            this.selectedIds.add(String(itemId));
            if (updateURL) {
                this.updateURL();
            }
            this.updateUI();
            this.updateBulkActionButtons();
        }

        removeSelection(itemId, updateURL = true) {
            this.selectedIds.delete(String(itemId));
            if (updateURL) {
                this.updateURL();
            }
            this.updateUI();
            this.updateBulkActionButtons();
        }

        clearAllSelections() {
            console.log('SelectionManager: clearAllSelections() called');
            console.log('Before clear - selected IDs:', Array.from(this.selectedIds));
            console.log('Current URL before clear:', window.location.href);

            this.selectedIds.clear();
            console.log('After clear - selected IDs:', Array.from(this.selectedIds));

            this.updateURL(); // This will remove selected_ids from URL when selectedIds is empty
            console.log('URL after updateURL():', window.location.href);

            this.updateCheckboxes();
            this.updateUI();
            this.updateBulkActionButtons();

            console.log('clearAllSelections() completed');
        }

        selectAllOnPage() {
            const checkboxes = document.querySelectorAll('.item-selection');
            checkboxes.forEach(checkbox => {
                if (checkbox.value && checkbox.value !== 'on') {
                    this.selectedIds.add(String(checkbox.value));
                }
            });
            this.updateURL();
            this.updateCheckboxes();
            this.updateUI();
            this.updateBulkActionButtons();
        }

        updateURL() {
            console.log('updateURL() called');
            console.log('selectedIds.size:', this.selectedIds.size);
            console.log('maxUrlIds:', this.maxUrlIds);

            const url = new URL(window.location);
            console.log('Original URL:', url.href);

            if (this.selectedIds.size > 0 && this.selectedIds.size <= this.maxUrlIds) {
                console.log('Setting selected_ids in URL');
                url.searchParams.set('selected_ids', Array.from(this.selectedIds).join(','));
            } else if (this.selectedIds.size > this.maxUrlIds) {
                // Too many selections for URL, keep current URL parameter if it exists
                console.warn('Too many selections for URL storage. Consider using session storage.');
            } else {
                console.log('Deleting selected_ids from URL (selectedIds.size is 0)');
                url.searchParams.delete('selected_ids');
            }

            console.log('New URL before replaceState:', url.href);
            window.history.replaceState({}, '', url);
            console.log('Final URL after replaceState:', window.location.href);
        }

        updateCheckboxes() {
            console.log('updateCheckboxes() called');
            const checkboxes = document.querySelectorAll('.item-selection');
            console.log('Found checkboxes:', checkboxes.length);
            console.log('Current selectedIds:', Array.from(this.selectedIds));

            let updatedCount = 0;
            checkboxes.forEach((checkbox, index) => {
                if (checkbox.value && checkbox.value !== 'on') {
                    const shouldBeChecked = this.selectedIds.has(String(checkbox.value));
                    const wasChecked = checkbox.checked;

                    console.log(`Checkbox ${index} (${checkbox.value}): was ${wasChecked}, should be ${shouldBeChecked}`);

                    checkbox.checked = shouldBeChecked;

                    if (wasChecked !== shouldBeChecked) {
                        updatedCount++;
                        console.log(`Updated checkbox ${checkbox.value}: ${wasChecked} -> ${shouldBeChecked}`);
                    }
                }
            });

            console.log(`updateCheckboxes() completed. Updated ${updatedCount} checkboxes.`);
        }

        updateUI() {
            // Update the existing selection count display
            const countElement = document.getElementById('total-item-selected');
            if (countElement) {
                countElement.textContent = this.selectedIds.size;
            }

            // Show/hide the existing view-header-container based on selection count
            const viewHeaderContainer = document.getElementById('view-header-container');
            const viewContainer = document.getElementById('view-container');

            check_records_count();
        }

        updateBulkActionButtons() {
            // Enable/disable bulk action buttons based on selection
            const bulkButtons = document.querySelectorAll('.bulk-action-button');
            bulkButtons.forEach(button => {
                if (this.selectedIds.size > 0) {
                    button.classList.remove('disabled');
                    button.disabled = false;
                } else {
                    button.classList.add('disabled');
                    button.disabled = true;
                }
            });

            // Update bulk action forms with selected IDs
            this.updateBulkActionForms();
        }

        updateBulkActionForms() {
            // Update any forms that need the selected IDs
            const forms = document.querySelectorAll('form[data-bulk-action]');
            forms.forEach(form => {
                let hiddenInput = form.querySelector('input[name="selected_ids"]');
                if (!hiddenInput) {
                    hiddenInput = document.createElement('input');
                    hiddenInput.type = 'hidden';
                    hiddenInput.name = 'selected_ids';
                    form.appendChild(hiddenInput);
                }
                hiddenInput.value = Array.from(this.selectedIds).join(',');
            });
        }

        bindEvents() {
            // Bind to checkbox changes
            document.addEventListener('change', (event) => {
                if (event.target.classList.contains('item-selection')) {
                    const itemId = event.target.value;
                    if (itemId && itemId !== 'on') {
                        if (event.target.checked) {
                            this.addSelection(itemId);
                        } else {
                            this.removeSelection(itemId);
                        }
                    }
                }
            });

            // Bind clear all button
            document.addEventListener('click', (event) => {
                if (event.target.id === 'clear-all-selections') {
                    event.preventDefault();
                    this.clearAllSelections();
                }
            });
        }

        getSelectedIds() {
            return Array.from(this.selectedIds);
        }

        getSelectedCount() {
            return this.selectedIds.size;
        }
    }

    // Initialize selection manager when DOM is ready
    let selectionManager;

    function initializeSelectionManager() {
        console.log('Attempting to initialize SelectionManager...');
        const checkboxes = document.querySelectorAll('.item-selection');
        console.log('Checkboxes found during init attempt:', checkboxes.length);

        if (checkboxes.length === 0) {
            console.log('No checkboxes found yet, retrying in 500ms...');
            setTimeout(initializeSelectionManager, 500);
            return;
        }

        console.log('Checkboxes found, initializing SelectionManager...');
        selectionManager = new SelectionManager();

        // Make it globally available for other scripts
        window.selectionManager = selectionManager;

        // Initialize the UI state based on current selections
        selectionManager.updateUI();
        selectionManager.updateBulkActionButtons();

        console.log('SelectionManager initialization complete');

            // Add a single verification check after initialization
            setTimeout(() => {
                const checkboxes = document.querySelectorAll('.item-selection');
                let inconsistentCount = 0;

                checkboxes.forEach(checkbox => {
                    if (checkbox.value && checkbox.value !== 'on') {
                        const shouldBeChecked = selectionManager.selectedIds.has(String(checkbox.value));
                        const isChecked = checkbox.checked;

                        if (shouldBeChecked !== isChecked) {
                            inconsistentCount++;
                            console.log(`INCONSISTENCY: Checkbox ${checkbox.value} should be ${shouldBeChecked} but is ${isChecked}`);
                        }
                    }
                });

                if (inconsistentCount > 0) {
                    console.log(`Found ${inconsistentCount} inconsistent checkboxes. Re-updating...`);
                    selectionManager.updateCheckboxes();
                } else {
                    console.log('All checkboxes are consistent with selectedIds');
                }

                // Debug pagination links
                console.log('=== PAGINATION DEBUG ===');
                const allLinks = document.querySelectorAll('a');
                const paginationLinks = Array.from(allLinks).filter(link =>
                    link.href.includes('page=') ||
                    link.textContent.includes('Next') ||
                    link.textContent.includes('Previous') ||
                    link.textContent.includes('次') ||
                    link.textContent.includes('前')
                );
                console.log('Found pagination links:', paginationLinks.length);
                paginationLinks.forEach((link, index) => {
                    console.log(`Pagination link ${index}:`, {
                        href: link.href,
                        text: link.textContent.trim(),
                        onclick: link.onclick ? link.onclick.toString() : 'none'
                    });
                });

                // Add event listeners to pagination links
                paginationLinks.forEach(link => {
                    link.addEventListener('click', function(event) {
                        console.log('PAGINATION LINK CLICKED:', this.href);

                        // Try to preserve selections
                        if (window.selectionManager && window.selectionManager.getSelectedCount() > 0) {
                            const selectedIds = window.selectionManager.getSelectedIds();
                            console.log('Preserving selections:', selectedIds);

                            const url = new URL(this.href);
                            url.searchParams.set('selected_ids', selectedIds.join(','));
                            this.href = url.toString();

                            console.log('Updated URL:', this.href);
                        } else {
                            console.log('No selections to preserve');
                        }
                    });
                });

                console.log('=== END PAGINATION DEBUG ===');
            }, 1000); // Check once after 1 second

            // Add global click listener to handle clear all buttons
            document.addEventListener('click', function(event) {
                const target = event.target;
                const text = target.textContent || target.innerText;

                // Log clicks on buttons that might be "Clear All"
                if (text.includes('選択を解除') || text.includes('Clear All') || text.includes('選択') || target.onclick) {
                    console.log('Button clicked:', {
                        element: target,
                        text: text.trim(),
                        id: target.id,
                        className: target.className,
                        onclick: target.onclick ? target.onclick.toString() : 'none'
                    });

                    // Handle "選択を解除" (Clear All) clicks
                    if (text.trim() === '選択を解除' || text.trim() === 'Clear All') {
                        console.log('Clear All button detected - calling clearAllSelections()');
                        event.preventDefault();
                        if (window.selectionManager) {
                            window.selectionManager.clearAllSelections();
                        } else {
                            console.log('SelectionManager not available, using fallback');
                            deselect_all_objects();
                        }
                        return false;
                    }
                }
            });
    }

    document.addEventListener('DOMContentLoaded', function() {
        // Start initialization with a small delay
        setTimeout(initializeSelectionManager, 100);
    });
</script>

<script>
    // Enhanced checkbox handling with shift-click support and cross-page selection
    var index_1 = '';
    var index_2 = '';

    const checking_checkbox = (elem, event) => {
        // Handle shift-click for range selection
        if (event && event.shiftKey && index_1) {
            const check_inputs = document.getElementsByClassName('check_input');
            index_2 = elem;

            // Find positions of first and current checkboxes
            let pos_1 = -1;
            let pos_2 = -1;
            for (let i = 0; i < check_inputs.length; i++) {
                if (index_1 === check_inputs[i]) {
                    pos_1 = i;
                }
                if (index_2 === check_inputs[i]) {
                    pos_2 = i;
                }
            }

            // Select range of checkboxes
            if (pos_1 !== -1 && pos_2 !== -1) {
                const start = Math.min(pos_1, pos_2);
                const end = Math.max(pos_1, pos_2);
                const isChecked = elem.checked;

                for (let i = start; i <= end; i++) {
                    const checkbox = check_inputs[i];
                    checkbox.checked = isChecked;

                    // Update selection manager for each checkbox
                    if (window.selectionManager && checkbox.value && checkbox.value !== 'on') {
                        if (isChecked) {
                            window.selectionManager.addSelection(checkbox.value, false);
                        } else {
                            window.selectionManager.removeSelection(checkbox.value, false);
                        }
                    }
                }

                // Update URL once after range selection
                if (window.selectionManager) {
                    window.selectionManager.updateURL();
                    window.selectionManager.updateUI();
                    window.selectionManager.updateBulkActionButtons();
                }
            }
        } else {
            // Single checkbox click - let SelectionManager handle it via event listener
            // This will be handled by the SelectionManager's change event listener
        }

        // Update index_1 for next shift-click
        index_1 = elem;
    };

    // Override the existing functions to work with SelectionManager
    function select_all_objects() {
        if (window.selectionManager) {
            window.selectionManager.selectAllOnPage();
        } else {
            // Fallback to original behavior
            $('.check_input').prop('disabled', false);
            $('.check_input').prop('checked', true);
            document.getElementById('flag_all').checked = false;
            if (typeof check_records_count === 'function') {
                check_records_count();
            }
        }
    }

    function deselect_all_objects() {
        console.log('deselect_all_objects() called');
        if (window.selectionManager) {
            console.log('deselect_all_objects() - Using SelectionManager.clearAllSelections()');
            window.selectionManager.clearAllSelections();
        } else {
            console.log('deselect_all_objects() - Using fallback behavior');
            // Fallback to original behavior
            document.getElementById('flag_all').checked = false;
            $('.check_input').prop('disabled', false);
            $('.check_input').prop('checked', false);
            document.getElementById('view-header-container').classList.add('tw-hidden');
            document.getElementById('view-container').classList.remove('tw-hidden');
        }
    }

    function selectAllInView() {
        if (window.selectionManager) {
            // This would select all items in the view (across all pages)
            // For now, we'll select all on current page
            window.selectionManager.selectAllOnPage();
        } else {
            // Fallback to original behavior
            const totalItems = {{ paginator.count|default:0 }};
            document.getElementById('total-item-selected').innerText = totalItems;
            $('.check_input').prop('checked', true);
        }
        document.getElementById('flag_all').checked = true;
    }
</script>

<script>
    // Keyboard shortcut for opening the 'Create New' drawer (n key)
    document.addEventListener('keydown', function(event) {
        // Ignore if input, textarea, or contenteditable is focused
        const active = document.activeElement;
        if (active && (active.tagName === 'INPUT' || active.tagName === 'TEXTAREA' || active.isContentEditable)) {
            return;
        }

        // Check if the key pressed is 'n'
        if (event.key.toLowerCase() === 'n') {
            event.preventDefault(); // Prevent default 'n' behavior
            
            // Find the 'Create New' button (adjust selector if needed)
            const newButton = document.querySelector('.view_form_trigger'); 
            
            if (newButton) {
                newButton.click(); // Simulate click to open the drawer
            }
        }

        // type 's' to focus on the search input
        if (event.key.toLowerCase() === 's' && window.innerWidth >= 1024) {
            event.preventDefault(); // Prevent default 's' behavior

            // Find the 'Search' button (adjust selector if needed)
            const searchInput = document.querySelector('#base-search-input');
            
            if (searchInput) {
                searchInput.focus();
            }
        }


    });
</script>


{% include 'data/javascript/toggleSearch.html' %}

<style>
    .loading-spinner{
        display:none;
    }
    .htmx-request .loading-spinner{
        display:inline-block;
    }
    .htmx-request.loading-spinner{
        display:inline-block;
    }
</style>

<script>
    function check_permission_action(event, permission_type, ...args){

        let source = args.length > 0 ? args[0] : null;

        const checkInputs = document.querySelectorAll('.check_input:checked');

        let members = "{{group_members}}"
        members = members.split(',')
        const user_id = '{{request.user.id}}'
        const permission = '{{permission}}';
        const permission_list = permission.split('|');
        let scope = ''
        permission_list.forEach(p => {
            if (p.includes(permission_type)) {
                p_split = p.split('_');
                scope = p_split[0]
            }
        })

        let msg = '';
        let denied = false;
        for (let i = 0; i < checkInputs.length; i++) {
            var owner_id = checkInputs[i].dataset.owner;
            if (owner_id){
                if (scope == 'user'){
                    if (owner_id.toString() !== user_id.toString()) {
                        {% if LANGUAGE_CODE == 'ja' %}
                        msg = "操作が拒否されました。自分のアイテムのみ編集または削除することができます。";
                        {% else %}
                        msg = "Action denied. You are only allowed to edit or delete your own items.";
                        {% endif %}                    
                        checkInputs[i].click()
                        denied = true;
                    }
                } else if (scope == 'team'){
                    if (!members.includes(owner_id.toString())) {
                        {% if LANGUAGE_CODE == 'ja' %}
                        msg = "操作が拒否されました。自分または自分のチームに割り当てられたアイテムのみ編集または削除できます。";
                        {% else %}
                        msg = "Action denied. You can only edit or delete items assigned to you or your team.";
                        {% endif %}
                        checkInputs[i].click()
                        denied = true;
                    }
                } 
            }
        }
        if (denied) {
            event.preventDefault();
            event.stopImmediatePropagation();
            document.getElementById('permissionActionWarning').innerHTML = msg;
            setTimeout(() => {
                document.getElementById('permissionActionWarning').innerHTML = '';
            }, 4000);
            msg = ''
        } else if (source){
            const modalEl = document.getElementById(source);
            const modal = bootstrap.Modal.getOrCreateInstance(modalEl);
            modal.show();
        }

    }
</script>

{% endif %}
{% endblock %}



