{% extends 'base.html' %}
{% load i18n %}
{% load custom_tags %}
{% load humanize %}
{% load hosts %}
{% block content %}

{% include "data/utility/table-css.html" %}
{% include 'data/static/tootip-search-wrapper.html' %}
<div class="d-flex d-flex align-items-center mb-2" style="background-color: #FAFAFA !important;">
    {% include 'data/common/module-header-tabs.html' %}

    <div class="w-50 d-flex justify-content-end tw-mr-5" id="view-container">
        <div class="{% include "data/utility/header-action-button.html" %}">
            <button type="button" class="btn tw-font-[500] tw-rounded-l-lg tw-rounded-r-none tw-border-0 tw-bg-gray-200 hover:tw-bg-gray-300 max-md:tw-hidden w-125px align-items-center d-flex svg-icon-gray-600 py-1 justify-content-center px-1 create-view-settings-button"
                hx-get="{% url 'shopturbo_load_drawer' %}"
                hx-vals='{"module": "{{menu_key}}"}'
                hx-trigger="click"
                onclick="fillItemExportIds(this)"
                hx-target="#manage-contacts-view-settings-drawer"

                >
                <span class="svg-icon svg-icon-4">
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-upload" viewBox="0 0 16 16">
                        <path d="M.5 9.9a.5.5 0 0 1 .5.5v2.5a1 1 0 0 0 1 1h12a1 1 0 0 0 1-1v-2.5a.5.5 0 0 1 1 0v2.5a2 2 0 0 1-2 2H2a2 2 0 0 1-2-2v-2.5a.5.5 0 0 1 .5-.5"/>
                        <path d="M7.646 1.146a.5.5 0 0 1 .708 0l3 3a.5.5 0 0 1-.708.708L8.5 2.707V11.5a.5.5 0 0 1-1 0V2.707L5.354 4.854a.5.5 0 1 1-.708-.708z"/>
                    </svg>
                </span>
                <span class="">
                    {% if LANGUAGE_CODE == 'ja'%}
                    エクスポート
                    {% else %}
                    Export
                    {% endif %}
                </span>
            </button>
            {% if permission|check_permission:'edit' %}
                <button id='view-sync-items' type="button" class="{% include "data/utility/import-button.html" %}"
                    style="width: 130px"
                    hx-get="{% url 'shopturbo_load_drawer' %}"
                    hx-vals='{"module": "{{menu_key}}"}'
                    hx-trigger="click"
                    onclick="fillItemIds(this)"
                    hx-target="#manage-contacts-view-settings-drawer"
                    >
                    <span class="svg-icon svg-icon-4">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-download" viewBox="0 0 16 16">
                            <path d="M.5 9.9a.5.5 0 0 1 .5.5v2.5a1 1 0 0 0 1 1h12a1 1 0 0 0 1-1v-2.5a.5.5 0 0 1 1 0v2.5a2 2 0 0 1-2 2H2a2 2 0 0 1-2-2v-2.5a.5.5 0 0 1 .5-.5"/>
                            <path d="M7.646 11.854a.5.5 0 0 0 .708 0l3-3a.5.5 0 0 0-.708-.708L8.5 10.293V1.5a.5.5 0 0 0-1 0v8.793L5.354 8.146a.5.5 0 1 0-.708.708z"/>
                        </svg>
                    </span>
                    <span class="">
                        {% if LANGUAGE_CODE == 'ja'%}
                        インポート
                        {% else %}
                        Import
                        {% endif %}
                    </span>
                </button>
            {% endif %}
        </div>

        {% if permission|check_permission:'edit' %}
            <button class="w-100px align-items-center d-flex btn btn-primary btn-md py-1 rounded-1 view_form_trigger shopturbo-create-wizard-button" type="button"
                hx-get="{% url 'shopturbo_load_drawer' %}"
                hx-vals = '{"drawer_type": "warehouse", "module": "{{menu_key}}"}'
                hx-target="#shopturbo-create-drawer-content"
                hx-indicator=".loading-drawer-spinner"
                style="height: 32px;"
                >
                <span class="svg-icon svg-icon-4">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <rect opacity="0.3" x="2" y="2" width="20" height="20" rx="10" fill="currentColor"/>
                        <rect x="10.8891" y="17.8033" width="12" height="2" rx="1" transform="rotate(-90 10.8891 17.8033)" fill="currentColor"/>
                        <rect x="6.01041" y="10.9247" width="12" height="2" rx="1" fill="currentColor"/>
                    </svg>
                </span>

                <span class="fs-7 ps-1 fw-bolder w-85px">
                    {% if LANGUAGE_CODE == 'ja'%}
                    新規
                    {% else %}
                    New
                    {% endif %}
                </span>
            </button>
        {% endif %}
    </div>
</div>
{% if permission == 'hide' %}
    {% include 'data/static/no-access-page.html' %}
{% else %}

{% include "data/common/advance_search/advance-search-style.html" %}

{% if permission|check_permission:'edit' %}
    <div id='modal-load'
        hx-vals='{"object_type": "{{object_type}}","module": "{{menu_key}}","view_id": "{{view_id}}"}'
        hx-get="{% url 'get_bulk_update_properties' %}" 
        hx-trigger='load'
        hx-target="this">
    </div>
{% endif %}

<div class="w-100 tw-pl-2">
    {% include 'data/common/permission-action-warning-message.html' %}
    {% comment %} Views Part {% endcomment %}
    <div class="{% include "data/utility/tab-pane.html" %}" role="tabpanel" style="z-index:4 !important;">
        <div class="{% include "data/utility/table-nav.html" %}">
            <div class="{% include "data/utility/table-content.html" %}" id="view-container-1">
                {% comment %} Desktop {% endcomment %}
                <div class="max-md:tw-hidden w-50 d-flex align-items-end me-3 w-100">
                    <div class="nav-item fs-6 text-gray-900">
                        <div class="d-flex align-items-center justify-content-center">
                            <button type="button" class="align-items-center justify-content-center d-flex btn text-gray-600 bg-transparent text-hover-primary tw-px-[5px] mb-2 tw-w-[30px] shopturbo-create-wizard-button"
                                hx-vals='{"object_type": "{{target}}", "view_button":"create", "module": "{{menu_key}}"}'
                                hx-get="{% url 'commerce_view_setting' %}"
                                hx-target="#shopturbo-create-drawer-content"
                                style="height: 26px;"
                            >
                                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="currentColor" class="bi bi-plus" viewBox="0 0 16 16">
                                    <path d="M8 4a.5.5 0 0 1 .5.5v3h3a.5.5 0 0 1 0 1h-3v3a.5.5 0 0 1-1 0v-3h-3a.5.5 0 0 1 0-1h3v-3A.5.5 0 0 1 8 4z"/>
                                </svg>
                            </button>
                        </div>
                    </div>

                    {% include "data/projects/partial-dropdown-view-menu.html" %}

                    {% for view in views %}
                        {% if view.title == 'main' %}
                            <div class="
                            nav-item me-2 d-flex align-items-center rounded-0 hover-boder-0
                            {% if view_filter.view.title == 'main' %}
                            active fw-bolder border-end-0 border-bottom border-bottom-3 border-primary
                            {% endif %}
                            ">
                                <a class="{% include "data/utility/view-menu-default-2.html" %}"
                                type="button"
                                 href="{% host_url 'load_object_page' menu_key object_type|object_type_slug:request host 'app' %}?view_id={{view.id}}">
                                    <span class="svg-icon svg-icon-muted svg-icon-2"><svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <path d="M22 12C22 17.5 17.5 22 12 22C6.5 22 2 17.5 2 12C2 6.5 6.5 2 12 2C17.5 2 22 6.5 22 12ZM12 6C8.7 6 6 8.7 6 12C6 15.3 8.7 18 12 18C15.3 18 18 15.3 18 12C18 8.7 15.3 6 12 6Z" fill="currentColor"/>
                                        </svg>
                                    </span>
                                </a>

                                {% comment %} edit {% endcomment %}
                                {% if current_view.id == view.id %}
                                <div class="w-20px nav-item justify-content-center d-flex fs-6 text-gray-900">
                                    <button type="button" class="{% include "data/utility/view-plus-link.html" %} create-view-settings-button"
                                        hx-vals='{"object_type": "{{target}}", "view_id":"{{view.id}}", "module": "{{menu_key}}"}'
                                        hx-get="{% url 'commerce_view_setting' %}"
                                        hx-target="#manage-contacts-view-settings-drawer"

                                        >
                                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-three-dots-vertical" viewBox="0 0 16 16">
                                            <path d="M9.5 13a1.5 1.5 0 1 1-3 0 1.5 1.5 0 0 1 3 0zm0-5a1.5 1.5 0 1 1-3 0 1.5 1.5 0 0 1 3 0zm0-5a1.5 1.5 0 1 1-3 0 1.5 1.5 0 0 1 3 0z"/>
                                        </svg>
                                        
                                    </button>
                                </div>
                                {% endif %}
                            </div>
                        {% endif %}
                    {% endfor %}

                    {% include 'data/projects/partial-view-menu.html' with target=target %}

                </div>

                {% comment %} Mobile View {% endcomment %}
                <div class="tw-hidden max-md:tw-flex tw-w-1/2">
                    <div class="d-flex align-items-center">
                        <!-- Example split danger button -->
                        <div class="btn-group mb-2">
                            <button type="button" class="tw-max-w-[90px] align-items-center d-flex btn bg-white border btn-md tw-px-[10px] create-view-settings-button"
                                style="height: 26px; border-radius: 0.475rem 0 0 0.475rem;"
                                hx-vals='{"object_type": "{{target}}", "view_id":"{{view.id}}", "module": "{{menu_key}}"}'
                                hx-get="{% url 'commerce_view_setting' %}"
                                hx-target="#manage-contacts-view-settings-drawer"
                                hx-trigger="click"
                                hx-swap="innerHTML"
                            >
                                <span class="fs-7 ps-1 fw-bolder tw-text-ellipsis tw-overflow-hidden tw-text-nowrap">
                                    {% if view_filter.view.title %}
                                        {% if view_filter.view.title == 'main' %}
                                            {% if LANGUAGE_CODE == 'ja'%}ビュー{% else %}View{% endif %}
                                        {% else %}
                                            {{ view_filter.view.title }}
                                        {% endif %}
                                    {% else %}
                                        {% if LANGUAGE_CODE == 'ja'%}ビュー{% else %}View{% endif %}
                                    {% endif %}
                                </span>
                            </button>
                            <button type="button"
                                class="tw-w-[30px] align-items-center d-flex btn btn-white border btn-md  dropdown-toggle py-1 tw-pl-2 dropdown-toggle-split"
                                data-bs-toggle="dropdown"
                                aria-expanded="false"
                                style="height: 26px; border-radius: 0 0.475rem 0.475rem 0;"
                            >
                                <span class="svg-icon svg-icon-4">
                                    <svg width=24 height=24 xmlns="http://www.w3.org/2000/svg" viewBox="0 0 320 512" fill="currentColor">
                                        <path d="M137.4 374.6c12.5 12.5 32.8 12.5 45.3 0l128-128c9.2-9.2 11.9-22.9 6.9-34.9s-16.6-19.8-29.6-19.8L32 192c-12.9 0-24.6 7.8-29.6 19.8s-2.2 25.7 6.9 34.9l128 128z"/>
                                    </svg>
                                </span>
                            </button>
                            <ul class="dropdown-menu tw-max-w-[180px] tw-max-h-[300px] tw-overflow-y-scroll">
                                <li><a class="dropdown-item tw-text-ellipsis tw-overflow-hidden" href="{% url 'shopturbo_inventory_warehouse' %}">{% if LANGUAGE_CODE == 'ja' %}デフォルト{% else %}Default View{% endif %}</a></li>
                                <li><a class="dropdown-item tw-text-ellipsis tw-overflow-hidden {% if request.GET.status == 'archived' %}active fw-bolder{% endif %}" href="{% url 'shopturbo_inventory_warehouse' %}?status=archived">{% if LANGUAGE_CODE == 'ja' %}アーカイブ{% else %}Archived{% endif %}</a></li>
                                <div class="dropdown-divider"></div>
                                {% for view in views %}
                                    {% if view.title != 'main' %}
                                        {% if view.title %}
                                            <li><a class="dropdown-item tw-text-ellipsis tw-overflow-hidden" href="{% url 'shopturbo_inventory_warehouse' %}?view_id={{view.id}}">{{view.title}}</a></li>
                                        {% endif %}
                                    {% endif %}
                                {% endfor %}
                            </ul>
                        </div>
                        <div class="fs-6 text-gray-900 mb-2">
                            <button type="button" class="tw-border-0 tw-bg-transparent tw-text-gray-900 shopturbo-create-wizard-button"
                                hx-vals='{"object_type": "{{target}}", "view_button":"create", "module": "{{menu_key}}"}'
                                hx-get="{% url 'commerce_view_setting' %}"
                                hx-target="#shopturbo-create-drawer-content"
                                hx-trigger="click"
                                hx-swap="innerHTML">
                                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="currentColor" class="bi bi-plus" viewBox="0 0 16 16">
                                    <path d="M8 4a.5.5 0 0 1 .5.5v3h3a.5.5 0 0 1 0 1h-3v3a.5.5 0 0 1-1 0v-3h-3a.5.5 0 0 1 0-1h3v-3A.5.5 0 0 1 8 4z"/>
                                </svg>
                            </button>
                        </div>
                    </div>
                </div>


                <div class="d-flex w-50 justify-content-end">
                    <div class="max-md:tw-hidden tw-flex me-2">
                        <div class="mb-2 search-wrapper expanded">
                            <div class="d-flex align-items-center">
                                <form id="filter-form-search" method="get" class="w-100">
                                    <div class="d-flex mb-0 position-relative align-items-center" style="height: 26px;">
                                        <span class="svg-icon svg-icon-3 search-icon-view">
                                            <svg width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <path d="M13 13L10.1 10.1M11.6667 6.33333C11.6667 9.27885 9.27885 11.6667 6.33333 11.6667C3.38781 11.6667 1 9.27885 1 6.33333C1 3.38781 3.38781 1 6.33333 1C9.27885 1 11.6667 3.38781 11.6667 6.33333Z" stroke="#8E8C95" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                                            </svg>
                                        </span>
                                        <input
                                        id="base-search-input" type="text" name="q" class="form-control bg-white ps-12 pe-16 h-25px tw-rounded-lg"
                                        value="{% if advance_search_filter %}{% for k, v in advance_search_filter.items %}{% if 'customer' in k and k|split:'|'|length > 1 %}{% with column_display=k|split:'|'|search_custom_field_object_order_customer:request %}{{column_display.name}}{% endwith %}{% else %}{% with args=k|add:'|'|add:object_type %}{% with column_display=args|get_column_display:request %}{{column_display.name}}{% endwith %}{% endwith %}{% endif %} {% translate_lang v.key|display_filter_rule:LANGUAGE_CODE LANGUAGE_CODE %} {{ v.value }}{% if not forloop.last %}, {% endif %}{% endfor %}{% elif search_q %}{{ search_q }}{% else %}{% endif %}"
                                        placeholder={% if LANGUAGE_CODE == 'ja' %} "検索" {% else %} "Search" {% endif %}
                                        onkeypress="if (event.keyCode == 13)document.forms['filter-form-search'].submit();"
                                        >
                                        {% if request.GET.status == 'archived' %}
                                        <input type="hidden" value="archived" name="status">
                                        {% endif %}
                                        <span class="svg-icon me-1 {% if advance_search.is_active %}svg-icon-primary{%else%}svg-icon-muted{% endif %} svg-icon-3 filter-icon filter-drawer-btn me-7"
                                            hx-get="{% url 'advance_search_drawer' %}"
                                            hx-vals='{"object_type": "{{object_type}}", "module": "{{menu_key}}", "view_id": "{{view_id}}"}'
                                            hx-indicator=".loading-drawer-spinner"
                                            hx-target="#filter-drawer-content"
                                            hx-trigger="click"
                                            hx-swap="innerHTML"
                                        >
                                            <svg width="14" height="10" viewBox="0 0 14 10" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <path d="M3 5H11M1 1H13M5 9H9" stroke="#8E8C95" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                                            </svg>
                                        </span>
                                        <span class="svg-icon svg-icon-muted svg-icon-3 filter-icon filter-drawer-btn"
                                            hx-get="{% url 'advance_search_drawer' %}"
                                            hx-vals='{"object_type": "{{object_type}}", "module": "{{menu_key}}", "search_scope":"True", "view_id": "{{view_id}}"}'
                                            hx-indicator=".loading-drawer-spinner"
                                            hx-target="#filter-drawer-content"
                                            hx-trigger="click"
                                            hx-swap="innerHTML"
                                        >
                                            <svg width="14" height="12" viewBox="0 0 14 12" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <path d="M1 3.33301L9 3.33301M9 3.33301C9 4.43758 9.89543 5.33301 11 5.33301C12.1046 5.33301 13 4.43758 13 3.33301C13 2.22844 12.1046 1.33301 11 1.33301C9.89543 1.33301 9 2.22844 9 3.33301ZM5 8.66634L13 8.66634M5 8.66634C5 9.77091 4.10457 10.6663 3 10.6663C1.89543 10.6663 1 9.77091 1 8.66634C1 7.56177 1.89543 6.66634 3 6.66634C4.10457 6.66634 5 7.56177 5 8.66634Z" stroke="#8E8C95" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                                            </svg>
                                        </span>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>

                    <div class="{% include "data/utility/view-menu-search.html" %}">
                        <button
                            onclick="openSearch()"
                            class="tw-w-[28px] tw-h-[26px] tw-justify-center tw-items-center tw-border-0 tw-bg-transparent tw-mb-2 tw-cursor-pointer hover-tooltip" type="button"
                            >
                            <span class="search-wrapper-tooltip hover-tooltip-text">
                                {% if LANGUAGE_CODE == 'ja' %}検索{% else %}Search{% endif %}
                            </span>
                            <span class="tw-flex svg-icon svg-icon-3">
                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                                    <path d="M21.7 18.9L18.6 15.8C17.9 16.9 16.9 17.9 15.8 18.6L18.9 21.7C19.3 22.1 19.9 22.1 20.3 21.7L21.7 20.3C22.1 19.9 22.1 19.3 21.7 18.9Z" fill="black" />
                                    <path opacity="0.3" d="M11 20C6 20 2 16 2 11C2 6 6 2 11 2C16 2 20 6 20 11C20 16 16 20 11 20ZM11 4C7.1 4 4 7.1 4 11C4 14.9 7.1 18 11 18C14.9 18 18 14.9 18 11C18 7.1 14.9 4 11 4ZM8 11C8 9.3 9.3 8 11 8C11.6 8 12 7.6 12 7C12 6.4 11.6 6 11 6C8.2 6 6 8.2 6 11C6 11.6 6.4 12 7 12C7.6 12 8 11.6 8 11Z" fill="black" />
                                </svg>
                            </span>
                        </button>
                    </div>

                    <div class="{% include "data/utility/table-button.html" %}">
                        <button class="max-md:tw-block tw-hidden tw-w-[28px] tw-h-[26px] tw-justify-center tw-items-center tw-border-0 tw-bg-transparent tw-mb-2 tw-cursor-pointer create-view-settings-button hover-tooltip"
                            type="button"

                            hx-get="{% url 'shopturbo_load_drawer' %}"
                            hx-vals='{"module": "{{menu_key}}"}'
                            hx-trigger="click"
                            onclick="fillItemExportIds(this)"
                            hx-target="#manage-contacts-view-settings-drawer"

                            >
                            <span class="search-wrapper-tooltip hover-tooltip-text">
                                {% if LANGUAGE_CODE == 'ja' %}エクスポート{% else %}Export{% endif %}
                            </span>
                            <span class="tw-flex svg-icon svg-icon-3">
                                <svg xmlns="http://www.w3.-listorg/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                                    <path d="M21 22H3C2.4 22 2 21.6 2 21C2 20.4 2.4 20 3 20H21C21.6 20 22 20.4 22 21C22 21.6 21.6 22 21 22ZM13 13.4V3C13 2.4 12.6 2 12 2C11.4 2 11 2.4 11 3V13.4H13Z" fill="black"/>
                                    <path opacity="0.3" d="M7 13.4H17L12.7 17.7C12.3 18.1 11.7 18.1 11.3 17.7L7 13.4Z" fill="black"/>
                                </svg>
                            </span>
                        </button>
                    </div>
                    <script>
                        function fillItemExportIds(elm) {
                            // Call your JavaScript function to generate the account IDs dynamically
                            var checkboxes = document.querySelectorAll('input[type="checkbox"]');
                            var checkedIds = [];
                            checkboxes.forEach(function(checkbox) {
                                if (checkbox.checked) {
                                    checkedIds.push(checkbox.value);
                                }
                            });
                            var itemIds = checkedIds;
                            itemIds = itemIds.filter(id => id !== 'on');
                            // Now set the hx-vals attribute with the updated account IDs
                            elm.setAttribute('hx-vals', '{"drawer_type":"view-sync-locations","page": "items","import_export_type":"export","view_id":"{{view_filter.view.id}}", "inventory_warehouse_ids":"' + itemIds + '", "module": "{{menu_key}}"}');
                        }
                    </script>

                    {% if permission|check_permission:'edit' %}

                    <div class="{% include "data/utility/table-button.html" %}">
                        <script>
                            function fillItemIds(elm) {
                                // Call your JavaScript function to generate the account IDs dynamically
                                var checkboxes = document.querySelectorAll('input[type="checkbox"]');
                                var checkedIds = [];
                                checkboxes.forEach(function(checkbox) {
                                    if (checkbox.checked) {
                                        checkedIds.push(checkbox.value);
                                    }
                                });
                                var itemIds = checkedIds;
                                itemIds = itemIds.filter(id => id !== 'on');
                                // Now set the hx-vals attribute with the updated account IDs
                                elm.setAttribute('hx-vals', '{"drawer_type":"view-sync-locations","page": "items","import_export_type":"import", "inventory_warehouse_ids":"' + itemIds + '","module": "{{menu_key}}"}');
                            }
                        </script>
                    </div>
                    {% endif %}
                </div>
            </div>

            <div id="view-header-container" class="tw-hidden w-100">
                <div class="justify-content-between align-items-center flex-row d-flex">
                    <div class="w-100">
                        {% include 'data/common/select-all-in-view-button.html' %}
                        
                        {% if permission|check_permission:'edit' %}
                            {% comment %}
                            Currently warehouse does not have object action
                            {% endcomment %}
                            <button class="py-1 rounded-1 btn btn-sm btn-light-primary fw-bold mt-2 mb-1" onclick="bulk_check_toggle(),check_permission_action(event, 'edit', 'edit_bulk_modal')" 
                                data-bs-toggle="modal" data-bs-target="#edit_bulk_modal"
                                >
                                    <span>
                                        {% if LANGUAGE_CODE == 'ja'%}
                                        編集
                                        {% else %}
                                        Edit
                                        {% endif %}
                                    </span>
                            </button>
                            <button class="py-1 rounded-1 btn btn-sm btn-light-info fw-bold mt-2 mb-1 create-view-settings-button d-none" type="button"
                                hx-get="{% host_url 'inventory_bulk_action_drawer' host 'app' %}"
                                hx-vals = 'js:{"section":"bulk-action", "inventory_ids": getSelectedInventory(), "module": "{{menu_key}}"}'
                                hx-target="#manage-contacts-view-settings-drawer"
                                hx-indicator=".loading-drawer-spinner"
                                style="border-radius: 0.475rem 0 0 0.475rem;"
                                onclick="check_permission_action(event, 'edit')"
                                >
                                {% if LANGUAGE_CODE == 'ja'%}
                                アクション
                                {% else %}
                                Action
                                {% endif %}
                            </button>

                            <button class="py-1 rounded-1 btn btn-sm btn-light-warning fw-bold mt-2 mb-1" onclick="check_permission_action(event, 'edit')" name="bulk_duplicate" type="submit" form="order-form">
                                {% if LANGUAGE_CODE == 'ja'%}
                                複製
                                {% else %}
                                Duplicate
                                {% endif %}
                            </button>

                            <script>
                                function getSelectedInventory() {
                                    var selectedItems = [];
                                    var classNameElements = document.getElementsByClassName("inventory-selection");
                                    if (classNameElements){
                                        classNameElements.forEach(function(classNameElement) {
                                            if (classNameElement.checked) {
                                                selectedItems.push(classNameElement.value);
                                            }
                                        });
                                    }
                                    console.log(selectedItems)
                                    return selectedItems;
                                }
                            </script>
                        {% endif %}
                        {% if permission|check_permission:'archive' %}
                        <button class="py-1 mt-2 mb-1 rounded-1 btn btn-sm btn-light-success fw-bold me-2" onclick="check_permission_action(event, 'archive', 'manage_restore_bulk')">
                            {% if LANGUAGE_CODE == 'ja'%}
                            有効化
                            {% else %}
                            Activate
                            {% endif %}
                        </button>
                        <button class="py-1 mt-2 mb-1 btn btn-sm btn-light-danger rounded-1 fw-bold me-2"  onclick="check_permission_action(event, 'archive', 'manage_delete_bulk')">
                            {% if LANGUAGE_CODE == 'ja'%}
                            アーカイブ
                            {% else %}
                            Archive
                            {% endif %}
                        </button>
                        {% endif %}

                        <script>
                            document.body.addEventListener("hideTaskBulkActionBtn", function(evt){
                                document.getElementById('inventory-bulk-action-container').classList.add('tw-hidden')
                                document.getElementById('inventory-view-contianer').classList.remove('tw-hidden')
                            })
                        </script>

                    </div>
                    <div class="d-flex w-50 align-items-center justify-content-end">
                        {% if permission|check_permission:'edit' %}
                            <div class="{% include "data/utility/table-button.html" %}">
                                <button type="button" class="{% include "data/utility/gray-header-button.html" %} create-view-settings-button"
                                    hx-get="{% url 'shopturbo_load_drawer' %}"
                                    hx-vals='{"module": "{{menu_key}}"}'
                                    hx-trigger="click"
                                    onclick="fillItemExportIds(this)"
                                    hx-target="#manage-contacts-view-settings-drawer"

                                    >
                                    <span class="svg-icon svg-icon-4">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-upload" viewBox="0 0 16 16">
                                            <path d="M.5 9.9a.5.5 0 0 1 .5.5v2.5a1 1 0 0 0 1 1h12a1 1 0 0 0 1-1v-2.5a.5.5 0 0 1 1 0v2.5a2 2 0 0 1-2 2H2a2 2 0 0 1-2-2v-2.5a.5.5 0 0 1 .5-.5"/>
                                            <path d="M7.646 1.146a.5.5 0 0 1 .708 0l3 3a.5.5 0 0 1-.708.708L8.5 2.707V11.5a.5.5 0 0 1-1 0V2.707L5.354 4.854a.5.5 0 1 1-.708-.708z"/>
                                        </svg>
                                    </span>
                                    <span class="">
                                        {% if LANGUAGE_CODE == 'ja'%}
                                        エクスポート
                                        {% else %}
                                        Export
                                        {% endif %}
                                    </span>
                                </button>
                            </div>
                        {% endif %}
                    </div>
                </div>
                {% include 'data/common/select-all-in-view-record-msg.html' %}
            </div>
        </div>
    </div>
    {% comment %} End of Views {% endcomment %}


    <div class="{% if search_q %}max-md:tw-flex {% else %}max-md:tw-hidden {% endif %}tw-hidden tw-items-center tw-py-2 border-bottom border-bottom-1 tw-transition-all tw-duration-300" id="search-bar">
        <form id="filter-form-search" method="get" class="w-100">
            <div class="d-flex mb-0 position-relative align-items-center" style="height: 26px;">
                <span class="svg-icon svg-icon-3 search-icon-view">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                        <path d="M21.7 18.9L18.6 15.8C17.9 16.9 16.9 17.9 15.8 18.6L18.9 21.7C19.3 22.1 19.9 22.1 20.3 21.7L21.7 20.3C22.1 19.9 22.1 19.3 21.7 18.9Z" fill="black" />
                        <path opacity="0.3" d="M11 20C6 20 2 16 2 11C2 6 6 2 11 2C16 2 20 6 20 11C20 16 16 20 11 20ZM11 4C7.1 4 4 7.1 4 11C4 14.9 7.1 18 11 18C14.9 18 18 14.9 18 11C18 7.1 14.9 4 11 4ZM8 11C8 9.3 9.3 8 11 8C11.6 8 12 7.6 12 7C12 6.4 11.6 6 11 6C8.2 6 6 8.2 6 11C6 11.6 6.4 12 7 12C7.6 12 8 11.6 8 11Z" fill="black" />
                    </svg>
                </span>
                <input id="base-search-input" type="text" name="q" class="form-control bg-white ps-12"
                value="{% if advance_search_filter %}{% for k, v in advance_search_filter.items %}{% if 'customer' in k and k|split:'|'|length > 1 %}{% with column_display=k|split:'|'|search_custom_field_object_order_customer:request %}{{column_display.name}}{% endwith %}{% else %}{% with args=k|add:'|'|add:object_type %}{% with column_display=args|get_column_display:request %}{{column_display.name}}{% endwith %}{% endwith %}{% endif %} {% translate_lang v.key|display_filter_rule:LANGUAGE_CODE LANGUAGE_CODE %} {{ v.value }}{% if not forloop.last %}, {% endif %}{% endfor %}{% elif search_q %}{{ search_q }}{% else %}{% endif %}"
                placeholder={% if LANGUAGE_CODE == 'ja' %} "ロケーション検索" {% else %} "Search Location" {% endif %}
                onkeypress="if (event.keyCode == 13)document.forms['filter-form-search'].submit();"
                >
                {% if request.GET.status == 'archived' %}
                <input type="hidden" value="archived" name="status">
                {% endif %}
                <span class="svg-icon svg-icon-muted svg-icon-3 filter-icon filter-drawer-btn me-7"
                    hx-get="{% url 'advance_search_drawer' %}"
                    hx-vals='{"object_type": "{{object_type}}", "module": "{{menu_key}}", "search_scope":"True", "view_id": "{{view_id}}"}'
                    hx-indicator=".loading-drawer-spinner"
                    hx-target="#filter-drawer-content"
                    hx-trigger="click"
                    hx-swap="innerHTML"
                >
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                        <path fill="black" d="M19.43 12.98c.04-.32.07-.65.07-.98s-.03-.66-.07-.98l2.11-1.65a.5.5 0 0 0 .12-.63l-2-3.46a.5.5 0 0 0-.6-.22l-2.49 1a7.03 7.03 0 0 0-1.7-.98l-.38-2.65A.5.5 0 0 0 14 2h-4a.5.5 0 0 0-.5.45l-.38 2.65c-.63.25-1.21.57-1.74.98l-2.49-1a.5.5 0 0 0-.6.22l-2 3.46a.5.5 0 0 0 .12.63L4.57 11c-.04.32-.07.65-.07.98s.03.66.07.98l-2.11 1.65a.5.5 0 0 0-.12.63l2 3.46a.5.5 0 0 0 .6.22l2.49-1c.53.41 1.11.73 1.74.98l.38 2.65c.03.24.24.45.5.45h4c.26 0 .47-.21.5-.45l.38-2.65c.63-.25 1.21-.57 1.74-.98l2.49 1a.5.5 0 0 0 .6-.22l2-3.46a.5.5 0 0 0-.12-.63l-2.11-1.65zM12 15.5A3.5 3.5 0 1 1 12 8a3.5 3.5 0 0 1 0 7.5z"/>
                    </svg>
                </span>
                <span class="svg-icon me-1 {% if advance_search.is_active %}svg-icon-primary{%else%}svg-icon-muted{% endif %} svg-icon-3 filter-icon filter-drawer-btn"
                    hx-get="{% url 'advance_search_drawer' %}"
                    hx-vals='{"object_type": "{{object_type}}", "module": "{{menu_key}}", "view_id": "{{view_id}}"}'
                    hx-indicator=".loading-drawer-spinner"
                    hx-target="#filter-drawer-content"
                    hx-trigger="click"
                    hx-swap="innerHTML"
                >
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M21 7H3C2.4 7 2 6.6 2 6V4C2 3.4 2.4 3 3 3H21C21.6 3 22 3.4 22 4V6C22 6.6 21.6 7 21 7Z" fill="currentColor"/>
                        <path opacity="0.3" d="M21 14H3C2.4 14 2 13.6 2 13V11C2 10.4 2.4 10 3 10H21C21.6 10 22 10.4 22 11V13C22 13.6 21.6 14 21 14ZM22 20V18C22 17.4 21.6 17 21 17H3C2.4 17 2 17.4 2 18V20C2 20.6 2.4 21 3 21H21C21.6 21 22 20.6 22 20Z" fill="currentColor"/>
                    </svg>
                </span>
                <input type="hidden" value="{{view_id}}" name="view_id">
            </div>
        </form>
    </div>

    <div class="mb-2 d-none" id="d-select-additional-options">
        <div class="status bg-light-dark text-center pt-4 pb-3 px-5 rounded">

            {% if LANGUAGE_CODE == 'ja'%}
            このページの全てのレコードが選択されました。
            {% else %}
            All records on this page are selected.
            {% endif %}

            <a onclick="toggleText()"
                class="btn btn-dark"
                data-bs-toggle="collapse"
                id="select-additional-options-toggle"
                role="button"
                aria-expanded="false"
                aria-controls="collapseExample">
                {% if LANGUAGE_CODE == 'ja'%}
                全てのレコードを選択する
                {% else %}
                Select all records
                {% endif %}

                </a>
        </div>
    </div>


    <form method="POST" id="order-form">
        {% csrf_token %}

        <div class="pt-0 table-responsive">
            <table class="{% include "data/utility/table.html" %}">
                <thead class="{% include "data/utility/table-header.html" %}">
                    <tr class="align-middle">
                        <th class="min-w-40px"></th>
                        {% for column in view_filter.column|safe|string_list_to_list %}
                            <th {% if column == 'id_iw' %} class="{% include "data/utility/column-id-size.html" %}" {% endif %}>
                                {% with args=column|add:'|'|add:target %}
                                    {% with column_display=args|get_column_display:request %}
                                        {% if LANGUAGE_CODE == 'ja' %}
                                            {% if column_display.name|lower == 'location name' %}
                                                ロケーション
                                            {% elif column_display.name|lower == 'warehouse' %}
                                                倉庫
                                            {% elif column_display.name|lower == 'floor' %}
                                                フロア
                                            {% elif column_display.name|lower == 'zone' %}
                                                ゾーン
                                            {% elif column_display.name|lower == 'aisle' %}
                                                列
                                            {% elif column_display.name|lower == 'rack' %}
                                                連
                                            {% elif column_display.name|lower == 'shelf' %}
                                                段
                                            {% elif column_display.name|lower == 'bin' %}
                                                間口
                                            {% else %}
                                                {{column_display.name}}
                                            {% endif %}
                                        {% else %}
                                            {% if column_display.name|lower == 'ロケーション' %}
                                                Location Name
                                            {% elif column_display.name|lower == '倉庫' %}
                                                Warehouse
                                            {% elif column_display.name|lower == 'フロア' %}
                                                Floor
                                            {% elif column_display.name|lower == 'ゾーン' %}
                                                Zone
                                            {% elif column_display.name|lower == '列' %}
                                                Aisle
                                            {% elif column_display.name|lower == '連' %}
                                                Rack
                                            {% elif column_display.name|lower == '段' %}
                                                Shelf
                                            {% elif column_display.name|lower == '間口' %}
                                                Bin
                                            {% else %}
                                                {{column_display.name}}
                                            {% endif %}
                                        {% endif %}
                                    {% endwith %}
                                {% endwith %}
                            </th>
                            {% if column == 'id_iw' %}
                            <th class="" style="width: 20px;">
                            </th>
                            {% endif %}
                        {% endfor %}
                    </tr>
                </thead>
                <tbody class="fs-6">

                    {% for warehouse in shopturbo_warehouses %}
                    <tr>
                        {% comment %} Checkbox {% endcomment %}
                        <td class="fw-bold w-40px">
                            <input id="{{warehouse.id}}" class="form-check-input cursor-pointer check_input inventory-selection" type="checkbox" name="checkbox" value="{{warehouse.id}}" data-owner="{{warehouse.owner.user.id}}" onclick="checking_checkbox(this, event)"/>
                        </td>

                        {% for column in view_filter.column|safe|string_list_to_list %}
                            {% include 'data/shopturbo/locations-row-partial.html' with warehouse=warehouse row_type=column %}
                        {% endfor %}
                    </tr>
                    {% endfor %}

                </tbody>
            </table>

            <input name='flag_all' id='flag_all' class="flag_all" hidden type="checkbox" ></input>
            {% if view_id %}
            <input type="hidden" value="{{view_id}}" name="view_id">
            {% endif %}

            <div class="modal fade" tabindex="-1" id="manage_delete_bulk">
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header pb-0 border-0 justify-content-end">
                            <div class="btn btn-icon btn-sm btn-active-light-primary ms-2" data-bs-dismiss="modal" aria-label="Close">
                            </div>
                        </div>
                        <div class="modal-body pb-0">
                            <div class="mb-13 text-center">
                                <h3 class="modal-title">

                                    {% if LANGUAGE_CODE == 'ja'%}
                                    一括アーカイブの確認
                                    {% else %}
                                    Bulk Archive Confirmation
                                    {% endif %}

                                </h3>
                            </div>
                            <div class="border-bottom">
                                <div class="fv-rowd-flex flex-column mb-8">
                                    <label class="{% include 'data/utility/form-label.html' %}">
                                        <span class="">
                                            {% if LANGUAGE_CODE == 'ja'%}
                                            選択されたレコードをアーカイブしてもよろしいですか?
                                            {% else %}
                                            Are you sure to archive selected records?
                                            {% endif %}
                                        </span>
                                    </label>

                                </div>
                            </div>
                        </div>


                        <div class="modal-footer border-0">
                            <button name="bulk_delete_items" type="submit" class="btn btn-danger">

                                {% if LANGUAGE_CODE == 'ja'%}
                                    アーカイブ
                                    {% else %}
                                    Archive
                                    {% endif %}
                                </button>

                            </button>
                            <a data-bs-dismiss="modal" class="btn btn-dark">
                                {% if LANGUAGE_CODE == 'ja'%}
                                キャンセル
                                {% else %}
                                Cancel
                                {% endif %}
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <div class="modal fade" tabindex="-1" id="manage_restore_bulk">
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header pb-0 border-0 justify-content-end">
                            <div class="btn btn-icon btn-sm btn-active-light-primary ms-2" data-bs-dismiss="modal" aria-label="Close">
                            </div>
                        </div>
                        <div class="modal-body pb-0">
                            <div class="mb-13 text-center">
                                <h3 class="modal-title">

                                    {% if LANGUAGE_CODE == 'ja'%}
                                    一括有効化の確認
                                    {% else %}
                                    Bulk Activate Confirmation
                                    {% endif %}

                                </h3>
                            </div>
                            <div class="border-bottom">
                                <div class="fv-rowd-flex flex-column mb-8">
                                    <label class="{% include 'data/utility/form-label.html' %}">
                                        <span class="">
                                            {% if LANGUAGE_CODE == 'ja'%}
                                            これらのロケーションを有効にしてもよろしいですか?
                                            {% else %}
                                            Are you sure to activate these locations?
                                            {% endif %}
                                        </span>
                                    </label>

                                </div>
                            </div>
                        </div>


                        <div class="modal-footer border-0">
                            <button name="bulk_restore_items" type="submit" class="btn btn-success">
                                {% if LANGUAGE_CODE == 'ja'%}
                                有効化
                                {% else %}
                                Activate
                                {% endif %}
                            </button>
                            <a data-bs-dismiss="modal" class="btn btn-dark">
                                {% if LANGUAGE_CODE == 'ja'%}
                                キャンセル
                                {% else %}
                                Cancel
                                {% endif %}
                            </a>
                        </div>
                    </div>
                </div>
            </div>

        </div>
    </form>

    <div class="{% include "data/utility/pagination.html" %}">
        {% if LANGUAGE_CODE == 'ja'%}
        {{paginator_item_begin}}–{{paginator_item_end}} の {{paginator.count}} 件
        {% else %}
        Viewing {{paginator_item_begin}}–{{paginator_item_end}} of {{paginator.count}} results
        {% endif %}
        <div>
            {% if page_content.has_previous %}
                <a class="btn tw-bg-gray-200 hover:tw-bg-gray-300 w-100px" href="{% host_url 'load_object_page' menu_key object_type|object_type_slug:request host 'app' %}?page=1&{% query_transform %}">{% translate_lang "First" LANGUAGE_CODE %}</a>
                <a class="btn tw-bg-gray-200 hover:tw-bg-gray-300 w-100px" href="{% host_url 'load_object_page' menu_key object_type|object_type_slug:request host 'app' %}?page={{ page_content.previous_page_number }}&{% query_transform %}">{% translate_lang "Previous" LANGUAGE_CODE %}</a>
            {% endif %}

            {% if page_content.has_next %}
                <a class="btn tw-bg-gray-200 hover:tw-bg-gray-300 w-100px" href="{% host_url 'load_object_page' menu_key object_type|object_type_slug:request host 'app' %}?page={{ page_content.next_page_number }}&{% query_transform %}">{% translate_lang "Next" LANGUAGE_CODE %}</a>
                <a class="btn tw-bg-gray-200 hover:tw-bg-gray-300 w-100px" href="{% host_url 'load_object_page' menu_key object_type|object_type_slug:request host 'app' %}?page={{ page_content.paginator.num_pages }}&{% query_transform %}">{% translate_lang "Last" LANGUAGE_CODE %}</a>
            {% endif %}
        </div>
    </div>

</div>

<script>
    // Function to load script dynamically
    function loadScript(url, callback) {
        var script = document.createElement('script');
        script.type = 'text/javascript';
        script.src = url;
        script.onload = callback;
        script.onerror = function() {
            console.error('Failed to load script:', url);
        };
        document.head.appendChild(script);
    }

    // Check if jQuery is loaded, if not, load it
    function ensureJQuery(callback) {
        if (typeof window.jQuery === 'undefined') {
            console.log('jQuery not loaded, loading now...');
            loadScript('https://cdn.jsdelivr.net/jquery/latest/jquery.min.js', function() {
                console.log('jQuery loaded successfully');
                if (callback) callback();
            });
        } else {
            console.log('jQuery already loaded');
            if (callback) callback();
        }
    }

    // Check if DataTable is loaded, if not, load it
    function ensureDataTableScripts(callback) {
        if (typeof $.fn.DataTable === 'undefined') {
            console.log('DataTable not loaded, loading scripts...');
            loadScript('https://cdn.datatables.net/1.13.7/js/jquery.dataTables.min.js', function() {
                console.log('DataTable core loaded');
                loadScript('https://cdn.datatables.net/fixedcolumns/4.3.0/js/dataTables.fixedColumns.min.js', function() {
                    console.log('DataTable FixedColumns loaded');
                    if (callback) callback();
                });
            });
        } else {
            console.log('DataTable already loaded');
            if (callback) callback();
        }
    }

    // Initialize DataTable
    function initializeDataTable() {
        $(".table-content").DataTable({
            scrollX:        true,
            scrollY:        "75vh",
            scrollCollapse: true,
            fixedColumns:   {
                left: 2
            },
            ordering: false,
            searching: false,  // Hide the search bar
            paging: false,      // Hide pagination
            info: false,        // Hide the information text
            language: {
                emptyTable: "{% translate_lang 'No data available in table' LANGUAGE_CODE %}"
            }
        });
    }

    // Make sure jQuery and DataTables are properly initialized
    function ensureDataTable(callback) {
        // First ensure jQuery is loaded
        ensureJQuery(function() {
            // Then ensure DataTable scripts are loaded
            ensureDataTableScripts(function() {
                if (callback && typeof callback === 'function') {
                    callback();
                }
            });
        });
    }

    // Initialize when document is ready
    $(document).ready(function() {
        ensureDataTable(function() {
            initializeDataTable();
        });
    });

    function toggleText() {
        var x = document.getElementById("select-additional-options-toggle");
        var toggle_data = x.getAttribute('toggle-data')
        if (toggle_data !== "true") {
            {% if LANGUAGE_CODE == 'ja'%}
            x.innerHTML = "選択を解除";
            {% else %}
            x.innerHTML = "Clear All";
            {% endif %}

            $(".flag_all").each(function(index, element) {
                element.value = true
            });

            x.setAttribute('toggle-data',"true")

        } else {
            x.setAttribute('toggle-data',"false")

            $('input[type=checkbox]').prop('checked', false);

            //Hide
            var element_select_options = document.getElementById("d-select-additional-options");
            element_select_options.classList.add("d-none")

            x.innerHTML = "Select All {{paginator.count}} contacts in this sections";

            $(".flag_all").each(function(index, element) {
                element.value = false
            });
        }
    }

    function select_all() {
        $('#selectAll').prop('checked', !$('#selectAll').prop('checked'));
        if ($('#selectAll').prop('checked') == true) {
            $('input[type=checkbox]').prop('checked', false);

            var element_select_options = document.getElementById("d-select-additional-options");
            element_select_options.classList.add("d-none")

        } else {
            $('input[type=checkbox]').prop('checked', true);

            var element_select_options = document.getElementById("d-select-additional-options");
            element_select_options.classList.remove("d-none")
        }
    }

    var contactChecked = {}
    var first_id_check_box = null;

    {% include "data/common/open-drawer.js" %}
</script>

{% if warehouse_id %}
<a id="selected_show_items" class="d-none text-center mb-0 text-dark text-hover-primary fw-bolder cursor-pointer shopturbo-manage-wizard-button"
    hx-get="{% url 'shopturbo_load_drawer' %}"
    hx-vals = '{"drawer_type":"inventory-warehouse-manage", "warehouse_id":"{{warehouse_id}}", "module": "{{menu_key}}", "view_id": "{{view_id}}" }'
    hx-target="#shopturbo-drawer-content"
    hx-indicator=".loading-drawer-spinner,#shopturbo-drawer-content"
    hx-trigger="click"
></a>
<script>
    $(document).ready(function() {
        setTimeout(function() {
            document.getElementById('selected_show_items').click();
        }, 0);
    });
</script>
{% endif %}


<script>
    // Keyboard shortcut for opening the 'Create New' drawer (n key)
    document.addEventListener('keydown', function(event) {
        // Ignore if input, textarea, or contenteditable is focused
        const active = document.activeElement;
        if (active && (active.tagName === 'INPUT' || active.tagName === 'TEXTAREA' || active.isContentEditable)) {
            return;
        }

        // Check if the key pressed is 'n'
        if (event.key.toLowerCase() === 'n') {
            event.preventDefault(); // Prevent default 'n' behavior
            
            // Find the 'Create New' button (adjust selector if needed)
            const newButton = document.querySelector('.view_form_trigger'); 
            
            if (newButton) {
                newButton.click(); // Simulate click to open the drawer
            }
        }
    });
</script>

<script>
    function check_permission_action(event, permission_type, ...args){

        let source = args.length > 0 ? args[0] : null;

        const checkInputs = document.querySelectorAll('.check_input:checked');

        let members = "{{group_members}}"
        members = members.split(',')
        const user_id = '{{request.user.id}}'
        const permission = '{{permission}}';
        const permission_list = permission.split('|');
        let scope = ''
        permission_list.forEach(p => {
            if (p.includes(permission_type)) {
                p_split = p.split('_');
                scope = p_split[0]
            }
        })

        let msg = '';
        let denied = false;
        for (let i = 0; i < checkInputs.length; i++) {
            var owner_id = checkInputs[i].dataset.owner;
            if (owner_id){
                if (scope == 'user'){
                    if (owner_id.toString() !== user_id.toString()) {
                        {% if LANGUAGE_CODE == 'ja' %}
                        msg = "操作が拒否されました。自分のアイテムのみ編集または削除することができます。";
                        {% else %}
                        msg = "Action denied. You are only allowed to edit or delete your own items.";
                        {% endif %}                    
                        checkInputs[i].click()
                        denied = true;
                    }
                } else if (scope == 'team'){
                    if (!members.includes(owner_id.toString())) {
                        {% if LANGUAGE_CODE == 'ja' %}
                        msg = "操作が拒否されました。自分または自分のチームに割り当てられたアイテムのみ編集または削除できます。";
                        {% else %}
                        msg = "Action denied. You can only edit or delete items assigned to you or your team.";
                        {% endif %}
                        checkInputs[i].click()
                        denied = true;
                    }
                } 
            }
        }
        if (denied) {
            event.preventDefault();
            event.stopImmediatePropagation();
            document.getElementById('permissionActionWarning').innerHTML = msg;
            setTimeout(() => {
                document.getElementById('permissionActionWarning').innerHTML = '';
            }, 4000);
            msg = ''
        } else if (source){
            const modalEl = document.getElementById(source);
            const modal = bootstrap.Modal.getOrCreateInstance(modalEl);
            modal.show();
        }

    }
</script>


{% include 'data/javascript/toggleSearch.html' %}

{% endif %}

{% endblock %}

