{% load static %}
{% load humanize %}
{% load i18n %}
{% load hosts %}
{% load custom_tags %}
{% get_current_language as LANGUAGE_CODE %}


<div class="d-flex d-flex align-items-center mb-2" style="background-color: #FAFAFA !important;">
    {% include 'data/common/module-header-tabs.html' %}

    <div class="w-50 d-flex justify-content-end tw-mr-5" id="view-container">
        <div class="{% include "data/utility/header-action-button.html" %}">
            <button id="payment_wizard_button" type="button" class="btn tw-font-[500] tw-rounded-l-lg tw-rounded-r-none tw-border-0 tw-bg-gray-200 hover:tw-bg-gray-300 max-md:tw-hidden w-125px align-items-center d-flex svg-icon-gray-600 py-1 justify-content-center px-1"
                hx-vals='{"object_type": "{{object_type}}", "view_id":"{{current_view.id}}", "download_view":true, "import_export_type":"export"}'
                hx-get="{% url 'commerce_view_setting' %}" 
                hx-target="#expenses_form"
                hx-on="htmx:beforeSend: 
                    document.getElementById('expenses_form_lg').innerHTML = '';
                    "
                >

                <span class="svg-icon svg-icon-4">
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-upload" viewBox="0 0 16 16">
                        <path d="M.5 9.9a.5.5 0 0 1 .5.5v2.5a1 1 0 0 0 1 1h12a1 1 0 0 0 1-1v-2.5a.5.5 0 0 1 1 0v2.5a2 2 0 0 1-2 2H2a2 2 0 0 1-2-2v-2.5a.5.5 0 0 1 .5-.5"/>
                        <path d="M7.646 1.146a.5.5 0 0 1 .708 0l3 3a.5.5 0 0 1-.708.708L8.5 2.707V11.5a.5.5 0 0 1-1 0V2.707L5.354 4.854a.5.5 0 1 1-.708-.708z"/>
                    </svg>
                </span>
                <span class="">
                    {% if LANGUAGE_CODE == 'ja'%}
                    エクスポート
                    {% else %}
                    Export
                    {% endif %}
                </span>

            </button>
            {% if object_type == constant.TYPE_OBJECT_PURCHASE_ORDER or object_type == constant.TYPE_OBJECT_EXPENSE or object_type == constant.TYPE_OBJECT_BILL %}
                <button type="button" class="payment_wizard_button btn tw-font-[500] tw-rounded-l-none tw-rounded-r-lg tw-border-0 tw-bg-gray-200 hover:tw-bg-gray-300 max-md:tw-hidden w-125px align-items-center d-flex svg-icon-gray-600 py-1 justify-content-center px-1"
                    hx-vals='{"object_type": "{{object_type}}", "view_id":"{{current_view.id}}", "download_view":true, "import_export_type":"import"}'
                    hx-get="{% url 'commerce_view_setting' %}" 
                    hx-target="#expenses_form"
                    hx-on="htmx:beforeSend: 
                        document.getElementById('expenses_form_lg').innerHTML = '';
                        "
                    >

                    <span class="svg-icon svg-icon-4">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-download" viewBox="0 0 16 16">
                            <path d="M.5 9.9a.5.5 0 0 1 .5.5v2.5a1 1 0 0 0 1 1h12a1 1 0 0 0 1-1v-2.5a.5.5 0 0 1 1 0v2.5a2 2 0 0 1-2 2H2a2 2 0 0 1-2-2v-2.5a.5.5 0 0 1 .5-.5"/>
                            <path d="M7.646 11.854a.5.5 0 0 0 .708 0l3-3a.5.5 0 0 0-.708-.708L8.5 10.293V1.5a.5.5 0 0 0-1 0v8.793L5.354 8.146a.5.5 0 1 0-.708.708z"/>
                        </svg>
                    </span>
                    <span class="">
                        {% if LANGUAGE_CODE == 'ja'%}
                        インポート
                        {% else %}
                        Import
                        {% endif %}
                    </span>

                </button>
            {% endif %}
        </div>

        {% if permission|check_permission:'edit' %}
        <div class="btn-group tw-h-[32px]" data-bs-toggle="tooltip" data-bs-placement="right" data-bs-delay-show="1000" data-kt-initialized="1">
            <button class="w-100px align-items-center d-flex btn btn-primary btn-md py-1 rounded-1 view_form_trigger"
                {% if object_type == constant.TYPE_OBJECT_BILL  %}
                    style="border-radius: 0.475rem 0 0 0.475rem !important;"
                    hx-vals='{"object_type": "{{constant.TYPE_OBJECT_BILL}}", "drawer_type":"{{constant.TYPE_OBJECT_BILL}}", "module": "{{menu_key}}","set_id":"{{set_id}}"}'
                    hx-get="{% url 'bill_drawer' %}" 
                    hx-indicator=".loading-drawer-spinner,.expenses-form"
                    hx-target="#expenses_form"
                    hx-on::before-request="document.getElementById('expenses_form').innerHTML = ''"
                    id="payment_wizard_button"  
                {% elif object_type == constant.TYPE_OBJECT_EXPENSE %}
                    style="border-radius: 0.475rem 0 0 0.475rem !important;"
                    hx-vals='{"drawer_type":"expenses", "module": "{{menu_key}}","set_id":"{{set_id}}"}'
                    hx-get="{% url 'expense_drawer_new' %}" 
                    hx-indicator=".loading-drawer-spinner,.expenses-form"
                    hx-target="#expenses_form"
                    hx-on::before-request="document.getElementById('expenses_form').innerHTML = ''"
                    id="payment_wizard_button"  
                {% elif object_type == "purchaseorder" %}
                    style="border-radius: 0.475rem 0 0 0.475rem !important;"
                    hx-vals='{"drawer_type":"purchaseorder", "view_id":"{{current_view.id}}", "module" : "{{menu_key}}", "set_id":"{{set_id}}"}'
                    hx-get="{% url 'purchase_drawer' %}" 
                    hx-indicator=".loading-drawer-spinner,.procurement-form"
                    hx-target="#procurement_form" 
                    hx-on::before-request="document.getElementById('procurement_form').innerHTML = ''"
                    hx-swap="innerHTML"
                    id="procurement_wizard_button"  
                {% elif object_type == "purchaseitem" %}
                    hx-vals='{"drawer_type":"purchaseitem"}'
                    hx-get="{% url 'purchaseItemDrawer' %}" 
                    hx-indicator=".loading-drawer-spinner,.expenses-form"
                    hx-target="#expenses_form"
                    hx-on::before-request="document.getElementById('expenses_form').innerHTML = ''"
                    id="payment_wizard_button"  
                {% endif %}
                type="button">
                <span class="svg-icon svg-icon-4">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <rect opacity="0.3" x="2" y="2" width="20" height="20" rx="10" fill="currentColor"/>
                        <rect x="10.8891" y="17.8033" width="12" height="2" rx="1" transform="rotate(-90 10.8891 17.8033)" fill="currentColor"/>
                        <rect x="6.01041" y="10.9247" width="12" height="2" rx="1" fill="currentColor"/>
                    </svg>
                </span>
                <span class="fs-7 ps-1 fw-bolder w-85px">
                    {% if LANGUAGE_CODE == 'ja'%}
                    新規
                    {% else %}
                    New
                    {% endif %}
                </span>
            </button>
            {% if object_type == "purchaseorder" %}
                <button type="button" 
                    class="tw-w-[30px] align-items-center d-flex btn btn-primary btn-md dropdown-toggle py-1 tw-pl-2 dropdown-toggle-split" 
                    data-bs-toggle="dropdown" 
                    aria-expanded="false"
                    style="border-radius: 0 0.475rem 0.475rem 0;border-left: 1px solid #dee2e6;"
                >
                    <span class="svg-icon svg-icon-4">
                        <svg width=24 height=24 xmlns="http://www.w3.org/2000/svg" viewBox="0 0 320 512" fill="currentColor">
                            <path d="M137.4 374.6c12.5 12.5 32.8 12.5 45.3 0l128-128c9.2-9.2 11.9-22.9 6.9-34.9s-16.6-19.8-29.6-19.8L32 192c-12.9 0-24.6 7.8-29.6 19.8s-2.2 25.7 6.9 34.9l128 128z"/>
                        </svg>
                    </span>
                </button>
                <ul class="dropdown-menu tw-max-w-[180px] tw-max-h-[300px] tw-overflow-y-scroll">
                    {% for set in property_sets %}
                    <li>
      
                        <button class="dropdown-item tw-text-ellipsis tw-overflow-hidden class-procurement_wizard_button" type="button"

                            hx-vals='{"drawer_type":"purchaseorder", "view_id":"{{view_id}}", "set_id": "{{set.id}}"}'
                            hx-get="{% url 'purchase_drawer' %}" 
                            
                            hx-target=".target-procurement_form" 
                            hx-swap="innerHTML"
                            hx-trigger="click-manual"
                            onclick="trigger(this)"
                            style="border-radius: 0.475rem 0 0 0.475rem;"
                            >
                            {% if set.name %}
                                {{ set.name}}
                            {% else %}
                                {% if LANGUAGE_CODE == 'ja' %}デフォルト{% else %}Default{% endif %} 
                            {% endif %}
                        </button>
                    </li>
                    {% endfor %}
                </ul>  
            {% elif object_type == constant.TYPE_OBJECT_BILL %}
                <button type="button" 
                    class="tw-w-[30px] align-items-center d-flex btn btn-primary btn-md dropdown-toggle py-1 tw-pl-2 dropdown-toggle-split" 
                    data-bs-toggle="dropdown" 
                    aria-expanded="false"
                    style="border-radius: 0 0.475rem 0.475rem 0;border-left: 1px solid #dee2e6;"
                >
                    <span class="svg-icon svg-icon-4">
                        <svg width=24 height=24 xmlns="http://www.w3.org/2000/svg" viewBox="0 0 320 512" fill="currentColor">
                            <path d="M137.4 374.6c12.5 12.5 32.8 12.5 45.3 0l128-128c9.2-9.2 11.9-22.9 6.9-34.9s-16.6-19.8-29.6-19.8L32 192c-12.9 0-24.6 7.8-29.6 19.8s-2.2 25.7 6.9 34.9l128 128z"/>
                        </svg>
                    </span>
                </button>
                <ul class="dropdown-menu tw-max-w-[180px] tw-max-h-[300px] tw-overflow-y-scroll">
                    <li>
      
                        <button class="dropdown-item tw-text-ellipsis tw-overflow-hidden class-procurement_wizard_button" type="button"

                            {% comment %} hx-vals='{"drawer_type":{{constant.TYPE_OBJECT_BILL}}}' {% endcomment %}
                            hx-vals='{"object_type":"{{constant.TYPE_OBJECT_BILL}}"}'
                            hx-get="{% url 'purchase_order_bot' %}" 
                            
                            hx-target=".target-procurement_form" 
                            hx-swap="innerHTML"
                            hx-trigger="click-manual"
                            onclick="trigger(this)"
                            style="border-radius: 0.475rem 0 0 0.475rem;"
                            >
                            {% if set.name %}
                                {{ set.name}}
                            {% else %}
                                {% if LANGUAGE_CODE == 'ja' %}一括アップロード{% else %}Bulk Upload{% endif %} 
                            {% endif %}
                        </button>
                    </li>
                </ul>
            {% elif object_type == constant.TYPE_OBJECT_EXPENSE %}
                <button type="button" 
                    class="tw-w-[30px] align-items-center d-flex btn btn-primary btn-md dropdown-toggle py-1 tw-pl-2 dropdown-toggle-split" 
                    data-bs-toggle="dropdown" 
                    aria-expanded="false"
                    style="border-radius: 0 0.475rem 0.475rem 0;border-left: 1px solid #dee2e6;"
                >
                    <span class="svg-icon svg-icon-4">
                        <svg width=24 height=24 xmlns="http://www.w3.org/2000/svg" viewBox="0 0 320 512" fill="currentColor">
                            <path d="M137.4 374.6c12.5 12.5 32.8 12.5 45.3 0l128-128c9.2-9.2 11.9-22.9 6.9-34.9s-16.6-19.8-29.6-19.8L32 192c-12.9 0-24.6 7.8-29.6 19.8s-2.2 25.7 6.9 34.9l128 128z"/>
                        </svg>
                    </span>
                </button>
                <ul class="dropdown-menu tw-max-w-[180px] tw-max-h-[300px] tw-overflow-y-scroll">
                    <li>
        
                        <button class="dropdown-item tw-text-ellipsis tw-overflow-hidden class-procurement_wizard_button" type="button"

                            {% comment %} hx-vals='{"drawer_type":{{constant.TYPE_OBJECT_BILL}}}' {% endcomment %}
                            hx-vals='{"object_type":"expense"}'
                            hx-get="{% url 'purchase_order_bot' %}" 
                            
                            hx-target=".target-procurement_form" 
                            hx-swap="innerHTML"
                            hx-trigger="click-manual"
                            onclick="trigger(this)"
                            style="border-radius: 0.475rem 0 0 0.475rem;"
                            >
                            {% if set.name %}
                                {{ set.name}}
                            {% else %}
                                {% if LANGUAGE_CODE == 'ja' %}一括アップロード{% else %}Bulk Upload{% endif %} 
                            {% endif %}
                        </button>
                    </li>
                </ul>
            {% endif %}
            <script>
                {% comment %} selectContent = document.getElementById('item-checker') {% endcomment %}
                //htmx.trigger(selectContent, 'click-manual');
                function trigger(elm){
                    console.log("elm: ", elm)
                    htmx.trigger(elm, 'click-manual');
                }
            </script>
        </div>
        {% endif %}
    </div>
</div>
{% if permission == 'hide' %}
    {% include 'data/static/no-access-page.html' %}
{% else %}
<div class="w-100 tw-pl-2">
{% include 'data/common/permission-action-warning-message.html' %}
{% include 'data/bill/bill-view-menu.html' %}

    <form id="bulk-update" method="POST" action="{% host_url 'load_object_page' menu_key object_type|object_type_slug:request host 'app' %}">
        {% csrf_token %}
        <input hidden name='query' value='{% get_page_query %}'>
        <input hidden name='module' value='{{menu_key}}'>
        <input hidden name="view_id" value="{{current_view.id}}" />
        {% if object_type == constant.TYPE_OBJECT_PURCHASE_ORDER %}
            <input hidden name='flag_all' id="flag_all" type="checkbox" />
        {% endif %}
    
        {% include 'data/bill/bill-table-section.html'%}
    </form>
    {% endif %}
    
    {% include "data/common/paginator/paginator.html" with redirect_url=pagination_url %}
</div>

<script>
    function check_permission_action(event, permission_type, ...args){

        let source = args.length > 0 ? args[0] : null;
        
        const checkInputs = document.querySelectorAll('.check_input:checked');

        console.log(checkInputs)
        console.log(permission_type)
        let members = "{{group_members}}"
        members = members.split(',')
        const user_id = '{{request.user.id}}'
        const permission = '{{permission}}';
        const permission_list = permission.split('|');
        let scope = ''
        permission_list.forEach(p => {
            if (p.includes(permission_type)) {
                p_split = p.split('_');
                scope = p_split[0]
            }
        })

        let msg = '';
        let denied = false;
        for (let i = 0; i < checkInputs.length; i++) {
            var owner_id = checkInputs[i].dataset.owner;
            if (owner_id){
                if (scope == 'user'){
                    if (owner_id.toString() !== user_id.toString()) {
                        {% if LANGUAGE_CODE == 'ja' %}
                        msg = "操作が拒否されました。自分のアイテムのみ編集または削除することができます。";
                        {% else %}
                        msg = "Action denied. You are only allowed to edit or delete your own items.";
                        {% endif %}                    
                        checkInputs[i].click()
                        denied = true;
                        console.log(user_id)
                        console.log(owner_id)
                    }
                } else if (scope == 'team'){
                    if (!members.includes(owner_id.toString())) {
                        {% if LANGUAGE_CODE == 'ja' %}
                        msg = "操作が拒否されました。自分または自分のチームに割り当てられたアイテムのみ編集または削除できます。";
                        {% else %}
                        msg = "Action denied. You can only edit or delete items assigned to you or your team.";
                        {% endif %}
                        checkInputs[i].click()
                        denied = true;
                    }
                } 
            }
        }
        if (denied) {
            event.preventDefault();
            event.stopImmediatePropagation();
            document.getElementById('permissionActionWarning').innerHTML = msg;
            setTimeout(() => {
                document.getElementById('permissionActionWarning').innerHTML = '';
            }, 4000);
            msg = ''
        } else if (source){
            const modalEl = document.getElementById(source);
            const modal = bootstrap.Modal.getOrCreateInstance(modalEl);
            modal.show();
        }
        
    }
</script>